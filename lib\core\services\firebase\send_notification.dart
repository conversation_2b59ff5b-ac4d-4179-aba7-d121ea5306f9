import 'dart:convert';
// import 'package:http/http.dart' as http;
import 'package:googleapis_auth/auth_io.dart';
import 'package:flutter/services.dart' show rootBundle;

Future<void> sendFCMv1Notification({
  required String targetToken,
  required String title,
  required String body,
}) async {
  // قراءة ملف JSON من assets
  // FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  final jsonString = await rootBundle.loadString('assets/json/uac-noto-b29e74ac0958.json');

  // تحويل إلى Map للحصول على project_id
  final Map<String, dynamic> serviceAccountJson = json.decode(jsonString);

  // استخراج project_id
  final projectId = serviceAccountJson['project_id'];

  // تحويل JSON إلى ServiceAccountCredentials
  final serviceAccount = ServiceAccountCredentials.fromJson(serviceAccountJson);

  // نطاقات المصادقة المطلوبة لـ FCM
  const scopes = ['https://www.googleapis.com/auth/firebase.messaging'];

  // إنشاء عميل مصادق عليه
  final client = await clientViaServiceAccount(serviceAccount, scopes);

  // عنوان API للإرسال
  final url = Uri.parse('https://fcm.googleapis.com/v1/projects/$projectId/messages:send');

  // إنشاء رسالة FCM
  final message = {
    "message": {
      "token": targetToken,
      "notification": {
        "title": title,
        "body": body,
      },
      "data": {
        "click_action": "FLUTTER_NOTIFICATION_CLICK",
      }
    }
  };

  // إرسال الطلب
  final response = await client.post(
    url,
    headers: {'Content-Type': 'application/json'},
    body: jsonEncode(message),
  );

  // التحقق من الاستجابة
  if (response.statusCode == 200) {
    print('✅ Notification sent successfully');
  } else {
    print('❌ Failed to send notification: ${response.body}');
  }

  client.close();
}
