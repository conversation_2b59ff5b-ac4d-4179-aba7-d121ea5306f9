flutter_launcher_icons:
  # الأيقونة الأساسية لجميع المنصات
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/driver.jpeg"
  min_sdk_android: 21
  
  # تكوين الأيقونة التكيفية للأندرويد (دائرية)
  adaptive_icon_background: "#2196F3" # لون الخلفية الأزرق
  adaptive_icon_foreground: "assets/images/driver.jpeg" # الصورة الأمامية
  
  # إعدادات iOS
  ios:
    generate: true
    image_path: "assets/images/driver.jpeg"
    
  # إعدادات الويب
  web:
    generate: true
    image_path: "assets/images/driver.jpeg"
    background_color: "#2196F3"
    theme_color: "#2196F3"
  
  # إعدادات ويندوز
  windows:
    generate: true
    image_path: "assets/images/driver.jpeg"
    icon_size: 48
  
  # إعدادات ماك
  macos:
    generate: true
    image_path: "assets/images/driver.jpeg"
  
  # إعدادات لينكس (اختياري)
  linux:
    generate: true
    image_path: "assets/images/driver.jpeg"