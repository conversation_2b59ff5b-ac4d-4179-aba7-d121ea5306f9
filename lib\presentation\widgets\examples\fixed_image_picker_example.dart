import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/controller/profile_controller.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/presentation/widgets/profile/profile_avatar_widget.dart';

/// 🛠️ Simple Example: Fixed Image Picker Usage
/// 
/// This example demonstrates how to use the fixed image picker
/// with proper error handling and user cancellation support.
/// 
/// ✅ Features:
/// • No crashes on user cancellation
/// • Proper permission handling
/// • Clear user feedback
/// • Professional error handling
class FixedImagePickerExample extends StatelessWidget {
  const FixedImagePickerExample({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize the ProfileController
    final ProfileController controller = Get.put(ProfileController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('✅ Fixed Image Picker'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Header
              const Text(
                '🎯 Image Picker - All Issues Fixed!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryText,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              const Text(
                'Try cancelling or selecting images - no more crashes! 🎉',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.secondaryText,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 48),
              
              // Profile Avatar with fixed image picker
              ProfileAvatarWidget(
                radius: 80,
                showEditButton: true,
              ),
              
              const SizedBox(height: 32),
              
              // Manual test buttons
              Row(
                children: [
                  Expanded(
                    child: Obx(() => ElevatedButton.icon(
                      icon: controller.isImageLoading.value
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                              ),
                            )
                          : const Icon(Icons.camera_alt),
                      label: Text(
                        controller.isImageLoading.value ? 'جاري...' : 'كاميرا',
                        style: const TextStyle(color: AppColors.white),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      onPressed: controller.isImageLoading.value
                          ? null
                          : controller.pickImageFromCamera,
                    )),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  Expanded(
                    child: Obx(() => ElevatedButton.icon(
                      icon: controller.isImageLoading.value
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                              ),
                            )
                          : const Icon(Icons.photo_library),
                      label: Text(
                        controller.isImageLoading.value ? 'جاري...' : 'معرض',
                        style: const TextStyle(color: AppColors.white),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.accent,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      onPressed: controller.isImageLoading.value
                          ? null
                          : controller.pickImageFromGallery,
                    )),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Clear button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  icon: const Icon(Icons.clear),
                  label: const Text('مسح الصورة'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.error,
                    side: const BorderSide(color: AppColors.error),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  onPressed: controller.clearSelectedImage,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Status display
              Obx(() => Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: controller.selectedProfileImage.value != null
                      ? AppColors.success.withValues(alpha: 0.1)
                      : AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: controller.selectedProfileImage.value != null
                        ? AppColors.success
                        : AppColors.primary,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      controller.selectedProfileImage.value != null
                          ? Icons.check_circle
                          : Icons.info,
                      color: controller.selectedProfileImage.value != null
                          ? AppColors.success
                          : AppColors.primary,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        controller.selectedProfileImage.value != null
                            ? 'تم اختيار الصورة: ${controller.selectedProfileImage.value!.name}'
                            : 'لم يتم اختيار صورة بعد - جرب الكاميرا أو المعرض!',
                        style: const TextStyle(fontWeight: FontWeight.w500),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              )),
              
              const SizedBox(height: 24),
              
              // Test instructions
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '✅ Try these scenarios:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.success,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '• Tap camera → Cancel → No crash!\n'
                      '• Tap gallery → Cancel → No crash!\n'
                      '• Select image → Success message\n'
                      '• Deny permissions → Helpful guidance\n'
                      '• All errors handled gracefully',
                      style: TextStyle(
                        color: AppColors.success,
                        height: 1.4,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 🚀 Quick Usage in any screen:
/// 
/// ```dart
/// // 1. Initialize controller
/// final ProfileController controller = Get.put(ProfileController());
/// 
/// // 2. Use avatar widget (handles everything automatically)
/// ProfileAvatarWidget(radius: 60, showEditButton: true)
/// 
/// // 3. Or call methods manually
/// await controller.pickImageFromCamera();  // ✅ No crashes
/// await controller.pickImageFromGallery(); // ✅ No crashes
/// 
/// // 4. Check selected image
/// if (controller.selectedProfileImage.value != null) {
///   String imagePath = controller.selectedProfileImage.value!.path;
///   // Use image...
/// }
/// ```