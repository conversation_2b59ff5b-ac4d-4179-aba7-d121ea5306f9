import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  static const supabaseUrl = 'https://giaznlzoeqaokzlgyozd.supabase.co';
  static const supabaseKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdpYXpubHpvZXFhb2t6bGd5b3pkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2NDc4ODYsImV4cCI6MjA1OTIyMzg4Nn0.wOe4xCMwCzOueLU6JjITUyIaOK4IxfgNMm6LM5Pjl3Q';
  static Future<void> initialize() async {
    await Supabase.initialize(url: supabaseUrl, anonKey: supabaseKey);
  }

  static SupabaseClient get client => Supabase.instance.client;
}
