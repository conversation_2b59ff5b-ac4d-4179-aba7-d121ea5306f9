import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:driver_app/Data/models/activity_logs.dart';
import 'package:driver_app/core/controller/user_controller.dart';
import 'package:driver_app/core/responsive/active_lo.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';
// import 'package:driver_app/core/responsive/responsive_layout.dart';
import 'package:driver_app/core/responsive/responsive_padding.dart';
import 'package:driver_app/core/responsive/responsive_text.dart';
import 'package:driver_app/core/routes/app_routes.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/presentation/widgets/navigation/navigation_button_bar.dart';

class ActivityLogScreen extends StatefulWidget {
  final String driverId;

  const ActivityLogScreen({super.key, required this.driverId});

  @override
  _ActivityLogScreenState createState() => _ActivityLogScreenState();
}

class _ActivityLogScreenState extends State<ActivityLogScreen> {
  int _selectedIndex = 1;
  late Future<List<ActivityLogModel>> _activityLogsFuture;
  final _userController = Get.find<UserController>();

  DateTime? _selectedDate; // For date search
// Cache all logs for filtering

  @override
  void initState() {
    super.initState();
    _loadActivityLogs();
  }

  Future<void> _loadActivityLogs() async {
    setState(() {
      _activityLogsFuture = ActivityLogRepository().getDriverActivityLogs(
        widget.driverId,
      ).then((logs) {
        return logs;
      });
    });
  }

  void _onDateSelected(DateTime? date) {
    setState(() {
      _selectedDate = date;
    });
  }

  List<ActivityLogModel> _filterLogsByDate(List<ActivityLogModel> logs) {
    if (_selectedDate == null) return logs;
    return logs.where((log) {
      return log.createdAt.year == _selectedDate!.year &&
          log.createdAt.month == _selectedDate!.month &&
          log.createdAt.day == _selectedDate!.day;
    }).toList();
  }

  Map<DateTime, List<ActivityLogModel>> _groupLogsByDate(
    List<ActivityLogModel> logs,
  ) {
    final Map<DateTime, List<ActivityLogModel>> grouped = {};
    for (final log in logs) {
      final date = DateTime(
        log.createdAt.year,
        log.createdAt.month,
        log.createdAt.day,
      );
      if (!grouped.containsKey(date)) {
        grouped[date] = [];
      }
      grouped[date]!.add(log);
    }
    return grouped;
  }

  Widget _buildEmptyState() {
    return Center(
      child: ResponsivePadding.all(
        context,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_outlined,
              size: ResponsiveHelper.getResponsiveIconSize(context) * 2,
              color: Colors.grey[400],
            ),
            SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context)),
            ResponsiveText(
              'لا يوجد الانشطة حتى الآن',
              baseFontSize: 18,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(
              height: ResponsiveHelper.getResponsiveSpacing(context) / 2,
            ),
            ResponsiveText(
              'جميع الانشطة ستظهر هنا',
              baseFontSize: 14,
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateHeader(DateTime date) {
    final now = DateTime.now();
    final yesterday = now.subtract(const Duration(days: 1));

    String headerText;

    if (DateFormat('yyyy-MM-dd').format(date) ==
        DateFormat('yyyy-MM-dd').format(now)) {
      headerText = 'اليوم';
    } else if (DateFormat('yyyy-MM-dd').format(date) ==
        DateFormat('yyyy-MM-dd').format(yesterday)) {
      headerText = 'أمس';
    } else {
      headerText = DateFormat('EEEE, MMMM d').format(date);
    }

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveHelper.getResponsiveSpacing(context) / 2,
        vertical: ResponsiveHelper.getResponsiveSpacing(context) / 2,
      ),
      child: ResponsiveText(
        headerText,
        baseFontSize: 16,
        style: TextStyle(fontWeight: FontWeight.bold, color: AppColors.primary),
      ),
    );
  }

  Color _getActivityCardColor(String type) {
    switch (type) {
      case 'success':
        return AppColors.success.withOpacity(0.08);
      case 'error':
        return AppColors.error.withOpacity(0.08);
      case 'warning':
        return AppColors.warning.withOpacity(0.08);
      default:
        return AppColors.cardBackground;
    }
  }

  Widget _buildActivityCard(ActivityLogModel activity) {
    final double borderRadius = ResponsiveHelper.getResponsiveBorderRadius(
      context,
    );
    final Color cardColor = _getActivityCardColor(activity.action.toLowerCase());
    final Color textColor = AppColors.primaryText;

    return Card(
      elevation: 2,
      color: cardColor,
      margin: EdgeInsetsDirectional.only(
        bottom: ResponsiveHelper.getResponsiveSpacing(context),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        side: BorderSide(color: AppColors.primary.withOpacity(0.7), width: 1.2),
      ),
      child: InkWell(
        onTap: () => _showActivityDetails(activity),
        borderRadius: BorderRadius.circular(borderRadius),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius),
            boxShadow: [
              BoxShadow(
                color: AppColors.lightGrey.withOpacity(0.8),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsetsDirectional.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  _getActivityIcon(activity.action),
                  color: AppColors.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        activity.action, // Use action as title
                        style: TextStyle(
                          fontSize: 16, // Title font size
                          fontWeight: FontWeight.bold,
                          color: textColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6),
                      Text(
                        activity.description,
                        style: TextStyle(
                          fontSize: 14, // Description font size
                          color: textColor.withOpacity(0.85),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        DateFormat('yyyy-MM-dd – kk:mm').format(activity.createdAt),
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.secondaryText,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getActivityIcon(String action) {
    switch (action.toLowerCase()) {
      case 'maintenance':
        return Icons.build;
      case 'login':
        return Icons.login;
      case 'update':
        return Icons.update;
      default:
        return Icons.history;
    }
  }

  void _showActivityDetails(ActivityLogModel activity) {
    final double borderRadius = ResponsiveHelper.getResponsiveBorderRadius(
      context,
    );

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(borderRadius * 1.5),
        ),
      ),
      builder: (context) {
        return ResponsivePadding(
          mobilePadding: const EdgeInsets.all(16),
          tabletPadding: const EdgeInsets.all(24),
          desktopPadding: const EdgeInsets.all(32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: AppColors.primary.withOpacity(0.2),
                    radius: ResponsiveHelper.getValue(context, 24, 28, 32),
                    child: Icon(
                      _getActivityIcon(activity.action),
                      color: AppColors.primary,
                      size: ResponsiveHelper.getValue(context, 22, 26, 30),
                    ),
                  ),
                  SizedBox(
                    width: ResponsiveHelper.getResponsiveSpacing(context),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ResponsiveText(
                          activity.action,
                          baseFontSize: 18,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        ResponsiveText(
                          _formatDateTime(activity.createdAt),
                          baseFontSize: 14,
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Divider(
                height: ResponsiveHelper.getResponsiveSpacing(context) * 2,
              ),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(
                  ResponsiveHelper.getResponsiveSpacing(context),
                ),
                decoration: BoxDecoration(
                  color: AppColors.lightGrey.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(borderRadius),
                  border: Border.all(color: AppColors.primary.withOpacity(0.2)),
                ),
                child: ResponsiveText(activity.description, baseFontSize: 16),
              ),
              SizedBox(
                height: ResponsiveHelper.getResponsiveSpacing(context) * 1.5,
              ),
              SizedBox(
                width: double.infinity,
                height: ResponsiveHelper.getValue(context, 48, 56, 64),
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(borderRadius),
                    ),
                  ),
                  child: ResponsiveText(
                    'الغاء',
                    baseFontSize: 16,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
            ],
          ),
        );
      },
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final yesterday = now.subtract(const Duration(days: 1));

    if (DateFormat('yyyy-MM-dd').format(dateTime) ==
        DateFormat('yyyy-MM-dd').format(now)) {
      return 'اليوم ${DateFormat('HH:mm').format(dateTime)}';
    } else if (DateFormat('yyyy-MM-dd').format(dateTime) ==
        DateFormat('yyyy-MM-dd').format(yesterday)) {
      return 'أمس ${DateFormat('HH:mm').format(dateTime)}';
    } else {
      return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
         backgroundColor: const Color(0xFF2196F3),
      elevation: 0,
      surfaceTintColor: Colors.transparent,
        title: Text('سجل الأنشطة',
        style: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontFamily: 'Cairo',
        ),),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(
            Icons.account_circle,
            color: Colors.black38,
            size: 35,
          ),
          onPressed: () {
            Get.toNamed(AppRoutes.profile);
          },
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.notifications_outlined,
              color: Colors.white,
              size: ResponsiveHelper.getResponsiveIconSize(Get.context!),
            ),
            onPressed: () {
              // Get.toNamed(AppRoutes.notifications);
            },
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(68),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: GestureDetector(
              onTap: () async {
                final now = DateTime.now();
                final picked = await showDatePicker(
                  context: context,
                  initialDate: _selectedDate ?? now,
                  firstDate: DateTime(2000),
                  lastDate: DateTime(now.year + 1),
                  builder: (context, child) {
                    return Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.light(
                          primary: AppColors.primary,
                          onPrimary: Colors.white,
                          onSurface: AppColors.primaryText,
                        ),
                        textButtonTheme: TextButtonThemeData(
                          style: TextButton.styleFrom(
                            foregroundColor: AppColors.primary,
                          ),
                        ),
                      ),
                      child: child!,
                    );
                  },
                );
                if (picked != null) {
                  _onDateSelected(picked);
                }
              },
              child: Container(
                alignment: Alignment.centerRight,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.lightGrey ,
                  borderRadius: BorderRadius.circular(16.0),
                  border: Border.all(
                    color: _selectedDate != null ? AppColors.primary : AppColors.borderColor,
                    width: 1.3,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    const SizedBox(width: 12),
                    Icon(Icons.calendar_today_outlined, color: AppColors.secondaryIconColor, size: 22),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Text(
                          _selectedDate == null
                              ? 'ابحث بالتاريخ'
                              : DateFormat('yyyy-MM-dd').format(_selectedDate!),
                          style: TextStyle(
                            color: AppColors.primaryText,
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ),
                    if (_selectedDate != null)
                      IconButton(
                        icon: Icon(Icons.close, color: AppColors.error, size: 20),
                        onPressed: () => _onDateSelected(null),
                        splashRadius: 18,
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: NavigationButtonBar(
        items: [
          NavigationItem(
            icon: Icons.home,
            label: 'لوحة تحكم',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.dashboard),
          ),
          NavigationItem(
            icon: Icons.assignment,
            label:'سجل النشاط',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () {
              final driverId = _userController.currentUser?.driverDetails?.id;
              if (driverId != null) {
                Get.toNamed(
                  AppRoutes.activityLog,
                  arguments: {'driverId': driverId},
                );
              } else {
                Get.snackbar('خطأ', 'معرف السائق غير موجود');
              }
            },
          ),
          NavigationItem(
            icon: Icons.build,
            label: 'صيانة',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.maintenance),
          ),
          NavigationItem(
            icon: Icons.map,
            label: 'خريطة',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.mapScreen),
          ),
        ],
        currentIndex: _selectedIndex,
        onItemSelected: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        selectedItemColor: AppColors.primary,
        unselectedItemColor: Colors.grey,
        shadowColor: AppColors.primary.withValues(alpha: 0.1),
      ),
      body: RefreshIndicator(
        onRefresh: _loadActivityLogs,
        color: AppColors.primary,
        child: FutureBuilder<List<ActivityLogModel>>(
          future: _activityLogsFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(color: AppColors.primary),
                    SizedBox(
                      height: ResponsiveHelper.getResponsiveSpacing(context),
                    ),
                    ResponsiveText(
                      'نحميل الانشطة...',
                      baseFontSize: 16,
                      style: TextStyle(color: AppColors.secondaryText),
                    ),
                  ],
                ),
              );
            }

            if (snapshot.hasError) {
              return Center(child: Text('خطأ: ${snapshot.error}'));
            }

            if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return _buildEmptyState();
            }

            final logs = _filterLogsByDate(snapshot.data!);
            if (logs.isEmpty) {
              return _buildEmptyState();
            }
            final groupedActivities = _groupLogsByDate(logs);
            final sortedDates =
                groupedActivities.keys.toList()..sort((a, b) => b.compareTo(a));

            return ResponsivePadding(
              mobilePadding: const EdgeInsets.all(12),
              tabletPadding: const EdgeInsets.all(16),
              desktopPadding: const EdgeInsets.all(20),
              child: ListView.builder(
                itemCount: sortedDates.length,
                itemBuilder: (context, index) {
                  final date = sortedDates[index];
                  final activities = groupedActivities[date]!;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDateHeader(date),
                      SizedBox(
                        height:
                            ResponsiveHelper.getResponsiveSpacing(context) / 2,
                      ),
                      ...activities.map(
                        (activity) => _buildActivityCard(activity),
                      ),
                      SizedBox(
                        height: ResponsiveHelper.getResponsiveSpacing(context),
                      ),
                    ],
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
