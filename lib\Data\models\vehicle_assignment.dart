class VehicleAssignment {
  final int id;
  final int vehicleId;
  final int driverId;
  final String vehicleName;
  final String vehicleNumber;
  final String plateNumber;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;

  VehicleAssignment({
    required this.id,
    required this.vehicleId,
    required this.driverId,
    required this.vehicleName,
    required this.vehicleNumber,
    required this.plateNumber,
    required this.startDate,
    required this.endDate,
    required this.isActive,
  });

  factory VehicleAssignment.fromJson(Map<String, dynamic> json) {
    return VehicleAssignment(
      id: json['id'],
      vehicleId: json['vehicle_id'],
      driverId: json['driver_id'],
      vehicleName: json['vehicle_name'] ?? '',
      vehicleNumber: json['vehicle_number'] ?? '',
      plateNumber: json['plate_number'] ?? '',
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      isActive: json['is_active'] ?? false,
    );
  }
}
