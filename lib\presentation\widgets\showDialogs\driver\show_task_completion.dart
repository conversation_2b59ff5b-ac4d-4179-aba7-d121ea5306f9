import 'package:flutter/material.dart';

void showTaskCompletionDialog(BuildContext context, VoidCallback onOkPressed) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Success icon in a blue circle
              Container(
                padding: EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Color(0xFF86CBF5),
                ),
                child: Icon(
                  Icons.check,
                  size: 48,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: 20.0),

              // Success message
              Text(
                'تم إكمال المهمة بنجاح',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color(0xFF0864B5),
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              SizedBox(height: 24.0),

              // OK button
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onOkPressed();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(0xFF86CBF5),
                  padding: EdgeInsets.symmetric(horizontal: 30, vertical: 10),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: Text(
                  'حسنًا',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}
