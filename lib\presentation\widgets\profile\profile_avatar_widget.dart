import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/controller/profile_controller.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/presentation/widgets/bottom_sheets/image_picker_bottom_sheet.dart';

class ProfileAvatarWidget extends StatelessWidget {
  final double radius;
  final String? defaultImagePath;
  final bool showEditButton;

  const ProfileAvatarWidget({
    super.key,
    this.radius = 55.0,
    this.defaultImagePath = 'assets/images/logo2.png',
    this.showEditButton = true,
  });

  @override
  Widget build(BuildContext context) {
    final ProfileController profileController = Get.find<ProfileController>();

    return Hero(
      tag: 'profileImage',
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.2),
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  blurRadius: 15,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Obx(() => CircleAvatar(
              radius: ResponsiveHelper.getValue(
                context,
                radius,
                radius + 10,
                radius + 20,
              ),
              backgroundImage: _getImageProvider(profileController),
              child: profileController.isImageLoading.value
                  ? CircularProgressIndicator(
                      strokeWidth: 3,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                    )
                  : null,
            )),
          ),
          
          if (showEditButton)
            Positioned(
              right: 0,
              bottom: 0,
              child: Obx(() => Container(
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.7),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: Icon(
                    profileController.isImageLoading.value 
                        ? Icons.hourglass_empty 
                        : Icons.camera_alt,
                    color: Colors.white,
                    size: ResponsiveHelper.getResponsiveIconSize(context) * 0.8,
                  ),
                  onPressed: profileController.isImageLoading.value 
                      ? null 
                      : () => _showImagePicker(),
                ),
              )),
            ),
        ],
      ),
    );
  }

  ImageProvider _getImageProvider(ProfileController profileController) {
    if (profileController.selectedProfileImage.value != null) {
      return FileImage(File(profileController.selectedProfileImage.value!.path));
    } else if (defaultImagePath != null) {
      return AssetImage(defaultImagePath!);
    } else {
      // Fallback to a default placeholder
      return const AssetImage('assets/images/logo2.png');
    }
  }

  void _showImagePicker() {
    Get.bottomSheet(
      const ImagePickerBottomSheet(),
      isScrollControlled: true,
    );
  }
}