import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/controller/profile_controller.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';
import 'package:driver_app/core/responsive/responsive_text.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/theme/app_styles.dart';

class ImagePickerBottomSheet extends StatelessWidget {
  const ImagePickerBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final ProfileController profileController = Get.find<ProfileController>();
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Container(
        padding: EdgeInsets.all(
          ResponsiveHelper.getResponsivePadding(context),
        ),
        decoration: BoxDecoration(
          color: AppColors.cardBackground,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(
              ResponsiveHelper.getResponsiveBorderRadius(context) * 1.5,
            ),
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryText.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: EdgeInsets.only(
                bottom: ResponsiveHelper.getResponsiveSpacing(context) * 1.5,
              ),
              decoration: BoxDecoration(
                color: AppColors.dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Title
            ResponsiveText(
              'اختيار صورة الملف الشخصي',
              baseFontSize: ResponsiveHelper.getResponsiveFontSize(context, 18),
              style: AppStyles.getHeadingStyle(context).copyWith(
                color: AppColors.primaryText,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context)),
            
            ResponsiveText(
              'اختر طريقة لتحديث صورة ملفك الشخصي',
              baseFontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
              style: AppStyles.getBodyStyle(context).copyWith(
                color: AppColors.secondaryText,
              ),
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 2),
            
            // Options
            _buildImagePickerOption(
              context,
              profileController,
              icon: Icons.photo_library_outlined,
              title: 'اختيار من المعرض',
              subtitle: 'اختر صورة من معرض الصور',
              onTap: () => profileController.pickImageFromGallery(),
              color: AppColors.primary,
            ),
            
            SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context)),
            
            _buildImagePickerOption(
              context,
              profileController,
              icon: Icons.camera_alt_outlined,
              title: 'التقاط صورة',
              subtitle: 'التقط صورة جديدة باستخدام الكاميرا',
              onTap: () => profileController.pickImageFromCamera(),
              color: AppColors.accent,
            ),
            
            SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 2),
            
            // Cancel button
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Navigator.of(context).pop(),

                style: TextButton.styleFrom(
                  backgroundColor: AppColors.secondaryText.withValues(alpha: 0.1),
                  padding: EdgeInsets.symmetric(
                    vertical: ResponsiveHelper.getResponsiveSpacing(context) * 1.2,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      ResponsiveHelper.getResponsiveBorderRadius(context),
                    ),
                  ),
                ),
                child: ResponsiveText(
                  'إلغاء',
                  baseFontSize: ResponsiveHelper.getResponsiveFontSize(context, 16),
                  style: TextStyle(
                    color: AppColors.secondaryText,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            
            // Safe area padding
            SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      ),
    );
  }

  Widget _buildImagePickerOption(
    BuildContext context,
    ProfileController profileController,
    {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color color,
  }) {
    return Obx(() => Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.getResponsiveBorderRadius(context),
        ),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.getResponsiveBorderRadius(context),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(
            ResponsiveHelper.getResponsiveBorderRadius(context),
          ),
          onTap: profileController.isImageLoading.value ? null : onTap,
          child: Padding(
            padding: EdgeInsets.all(
              ResponsiveHelper.getResponsiveSpacing(context) * 1.2,
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(
                    ResponsiveHelper.getResponsiveSpacing(context) * 0.8,
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(
                      ResponsiveHelper.getResponsiveBorderRadius(context) * 0.8,
                    ),
                  ),
                  child: profileController.isImageLoading.value
                      ? SizedBox(
                          width: ResponsiveHelper.getResponsiveIconSize(context),
                          height: ResponsiveHelper.getResponsiveIconSize(context),
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(color),
                          ),
                        )
                      : Icon(
                          icon,
                          color: color,
                          size: ResponsiveHelper.getResponsiveIconSize(context),
                        ),
                ),
                
                SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context)),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ResponsiveText(
                        title,
                        baseFontSize: ResponsiveHelper.getResponsiveFontSize(context, 16),
                        style: AppStyles.getBodyStyle(context).copyWith(
                          color: AppColors.primaryText,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.right,
                      ),
                      SizedBox(height: 2),
                      ResponsiveText(
                        subtitle,
                        baseFontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
                        style: AppStyles.getBodyStyle(context).copyWith(
                          color: AppColors.secondaryText,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ],
                  ),
                ),
                
                Icon(
                  Icons.arrow_back_ios,
                  color: color.withValues(alpha: 0.7),
                  size: ResponsiveHelper.getResponsiveIconSize(context) * 0.8,
                ),
              ],
            ),
          ),
        ),
      ),
    ));
  }
}