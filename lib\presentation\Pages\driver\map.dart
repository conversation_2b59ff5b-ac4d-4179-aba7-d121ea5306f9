import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:driver_app/core/controller/user_controller.dart';
import 'package:driver_app/core/routes/app_routes.dart';
import 'package:driver_app/core/services/firebase/send_notification.dart';
// import 'package:driver_app/core/services/firebase/send_notification.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/presentation/widgets/navigation/navigation_button_bar.dart';
// import 'package:supabase_flutter/supabase_flutter.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  Location locationController = Location();
  final Completer<GoogleMapController> _mapController = Completer();
  int _selectedIndex = 3; // Initialize _selectedIndex
  final _userController =
      Get.find<UserController>(); // Define and initialize _userController
  LatLng? _currentPosition;
  Map<PolylineId, Polyline> polylines = {}; // <PolygonId, Polygon>
  DateTime lastSent = DateTime.fromMillisecondsSinceEpoch(0); // Track last sent time

  @override
  void initState() {
    super.initState();
    getLocationUpdate();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: NavigationButtonBar(
        items: [
          NavigationItem(
            icon: Icons.home,
            label: 'لوحة التحكم',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.dashboard),
          ),
          NavigationItem(
            icon: Icons.assignment,
            label: 'سجل النشاطات',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () {
              final driverId = _userController.currentUser?.driverDetails?.id;
              if (driverId != null) {
                Get.toNamed(
                  AppRoutes.activityLog,
                  arguments: {'driverId': driverId},
                );
              } else {
                Get.snackbar('خطأ', 'معرف السائق غير متوفر');
              }
            },
          ),
          NavigationItem(
            icon: Icons.build,
            label: 'صيانة',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.maintenance),
          ),
          NavigationItem(
            icon: Icons.map,
            label: 'خريطة',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.mapScreen),
          ),
        ],
        currentIndex: _selectedIndex,
        onItemSelected: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        selectedItemColor: AppColors.primary,
        unselectedItemColor: Colors.grey,
        shadowColor: AppColors.primary.withValues(alpha: 0.1),
      ),
      body:
          _currentPosition == null
              ? const Center(child: Text("افتح GPS لتشغيل الخريطة و تحديد موقعك"))
              : GoogleMap(
                onMapCreated:
                    (GoogleMapController controller) =>
                        _mapController.complete(controller),
                initialCameraPosition: CameraPosition(
                  target: _currentPosition!,
                  zoom: 5.0,
                ),
                markers: {
                  Marker(
                    markerId: MarkerId("الموقع الحالي"),
                    icon: BitmapDescriptor.defaultMarkerWithHue(
                      BitmapDescriptor.hueBlue,
                    ),
                    position: _currentPosition!,
                  ),
                },
              ),
      // floatingActionButton: IconButton(
      //   onPressed: () {
      //     sendtoNotification("تنبيه", 'تم إرسال إشعار');
      //   },
      //   icon: Icon(Icons.notifications),
      // ),
    );
  }

  Future<void> getLocationUpdate() async {
    bool serviceEnable;
    PermissionStatus permissionGranted;
    serviceEnable = await locationController.serviceEnabled();
    if (serviceEnable) {
      serviceEnable = await locationController.requestService();
    } else {
      return;
    }
    permissionGranted = await locationController.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await locationController.requestPermission();
      if (permissionGranted != PermissionStatus.granted) {
        return;
      }
    }
    locationController.onLocationChanged.listen((LocationData currentLocation) {
      // Update the map with the new location
      final now = DateTime.now();
      if (currentLocation.latitude != null &&
          currentLocation.longitude != null) {
        setState(() {
          _currentPosition = LatLng(
            currentLocation.latitude!,
            currentLocation.longitude!,
          );
          if (now.difference(lastSent).inSeconds >= 10) {
      lastSent = now;
      upsertDriverLocation(
        currentLocation.latitude!,
        currentLocation.longitude!,
      );
    }
          print(_currentPosition);
          _comeraToPosition(_currentPosition!);
        });
      }
    });
    // Location location = Location();
  }

  Future<void> _comeraToPosition(LatLng posision) async {
    final GoogleMapController controller = await _mapController.future;
    CameraPosition newCameraPosision = CameraPosition(
      target: posision,
      zoom: 10,
    );
    await controller.animateCamera(
      CameraUpdate.newCameraPosition(newCameraPosision),
    );
  }

  Future<void> sendtoNotification(String title, String des) async {
    final supabase = Supabase.instance.client;
    try {
          print("====================================================");

    final supervisorID = await supabase
        .from('drivers')
        .select('supervisor_id')
        .eq('id', _userController.currentUser?.driverDetails!.id ?? '')
        .single();

    final supervisorIdValue = supervisorID['supervisor_id'];
    print('Supervisor ID: $supervisorIdValue');

    final supervisorTokenRes = await supabase
        .from('supervisors')
        .select('user_id')
        .eq('id', supervisorIdValue);

    final supervisorUserId = supervisorTokenRes.first['user_id'];
    print('Supervisor User ID: $supervisorUserId');

    final tokenRes = await supabase
        .from('device_tokens')
        .select('token')
        .eq('user_id', supervisorUserId);

    final targetToken = tokenRes.first['token'];
    print('Target Token: $targetToken');
    print("====================================================");
      sendFCMv1Notification(
        targetToken:
            targetToken.toString(),
        title: title,
        body: des,
      );
    } catch (e) {
      print(e.toString());
    }
  }
Future<void> upsertDriverLocation(double latitude, double longitude) async {
  final supabase = Supabase.instance.client;
  final driverId = _userController.currentUser?.driverDetails?.id;

  if (driverId == null) {
    print("Driver ID not available");
    return;
  }

  try {
    final data = await supabase
        .from('driver_locations')
        .upsert({
          'driver_id': driverId,
          'latitude': latitude,
          'longitude': longitude,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .select();

    print("Location saved or updated: $data");
  } catch (e) {
    print("Exception during location update: $e");
  }
}

}
