class AppUser {
  final String id;
  final String email;
  final String fullName;
  final String role;
  final DriverDetails? driverDetails;

  AppUser({
    required this.id,
    required this.email,
    required this.fullName,
    required this.role,
    this.driverDetails,
  });

  AppUser copyWith({
    String? id,
    String? email,
    String? fullName,
    String? role,
    DriverDetails? driverDetails,
  }) {
    return AppUser(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      role: role ?? this.role,
      driverDetails: driverDetails ?? this.driverDetails,
    );
  }
}

class DriverDetails {
  final String id;
  final String licenseNumber;
  final String vehicleId;
  final String supervisorId;
  final String status;

  DriverDetails({
    required this.id,
    required this.licenseNumber,
    required this.vehicleId,
    required this.supervisorId,
    required this.status,
  });

  factory DriverDetails.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return DriverDetails(
        id: '',
        licenseNumber: '',
        vehicleId: '',
        supervisorId: '',
        status: 'not_working',
      );
    }

    return DriverDetails(
      id: json['id']?.toString() ?? '',
      licenseNumber: json['license_number']?.toString() ?? '',
      vehicleId: json['vehicle_id']?.toString() ?? '',
      supervisorId: json['supervisor_id']?.toString() ?? '',
      status: json['status']?.toString() ?? 'not_working',
    );
  }

  DriverDetails copyWith({
    String? id,
    String? licenseNumber,
    String? vehicleId,
    String? supervisorId,
    String? status,
  }) {
    return DriverDetails(
      id: id ?? this.id,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      vehicleId: vehicleId ?? this.vehicleId,
      supervisorId: supervisorId ?? this.supervisorId,
      status: status ?? this.status,
    );
  }
}

enum UserRole { Manager, Supervisor, Driver }

extension RoleExtension on UserRole {
  String get name {
    switch (this) {
      case UserRole.Manager:
        return 'Manager';
      case UserRole.Supervisor:
        return 'Supervisor';
      case UserRole.Driver:
        return 'Driver';
    }
  }

  static UserRole? fromString(String role) {
    switch (role.toLowerCase()) {
      case 'manager':
        return UserRole.Manager;
      case 'supervisor':
        return UserRole.Supervisor;
      case 'driver':
        return UserRole.Driver;
      default:
        return null;
    }
  }
}
