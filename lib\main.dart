import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/controller/user_controller.dart';
import 'package:driver_app/core/responsive/driver_cl.dart';
import 'package:driver_app/core/routes/supabase.dart';
import 'package:driver_app/core/services/auth_service.dart';
import 'package:driver_app/core/services/firebase/firebase_api.dart';
import 'package:driver_app/core/services/firebase/token_repository.dart';
import 'package:driver_app/core/services/noInternet_connection_app.dart';
import 'package:flutter/services.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/routes/app_routes.dart';
import 'package:driver_app/firebase_options.dart';
import 'package:driver_app/presentation/Pages/driver/auth/splash_screen.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

// 1. إنشاء قناة إشعارات للأندرويد
const AndroidNotificationChannel channel = AndroidNotificationChannel(
  'high_importance_channel', // معرف القناة
  'High Importance Notifications', // اسم القناة
  description: 'This channel is used for important notifications.', // وصف القناة
  importance: Importance.max, // الأهمية القصوى
);

// 2. دالة معالجة الإشعارات في الخلفية
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  print("📦 Background FCM message: ${message.messageId}");

  // عرض الإشعار حتى في الخلفية
  if (message.notification != null) {
    await _showNotification(message);
  }
}

// 3. دالة مساعدة لعرض الإشعارات
Future<void> _showNotification(RemoteMessage message) async {
  final notification = message.notification;
  
  if (notification == null) return;

  await flutterLocalNotificationsPlugin.show(
    notification.hashCode,
    notification.title,
    notification.body,
    NotificationDetails(
      android: AndroidNotificationDetails(
        channel.id,
        channel.name,
        channelDescription: channel.description,
        icon: '@mipmap/ic_launcher',
        importance: Importance.max,
        priority: Priority.high,
      ),
    ),
  );
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 1. تهيئة الخدمات الأساسية
  await _initializeCoreServices();

  // 2. إنشاء قناة الإشعارات
  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);

  // 3. تهيئة الإشعارات المحلية
  const AndroidInitializationSettings androidSettings =
      AndroidInitializationSettings('@mipmap/ic_launcher');
  await flutterLocalNotificationsPlugin.initialize(
    const InitializationSettings(android: androidSettings),
  );

  // 4. التعامل مع الإشعارات في الواجهة الأمامية
  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    print("📬 Foreground FCM message: ${message.messageId}");
    _showNotification(message); // عرض الإشعار مباشرة
  });

  // 5. التحقق من اتصال الإنترنت
  final connectivityResult = await Connectivity().checkConnectivity();
  final isConnected = connectivityResult != ConnectivityResult.none;

  if (!isConnected) {
    runApp(const NoInternetConnectionApp());
    return;
  }

  // 6. ضبط إعدادات الشاشة
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
    ),
  );

  // 7. تهيئة إعدادات اللغة
  final RxString currentLanguage = 'ar'.obs;
  Get.put(currentLanguage, permanent: true);

  // 8. مراقبة تغييرات الاتصال
  _setupConnectivityListener();

  // 9. تشغيل التطبيق
  runApp(MyApp(isConnected: isConnected));
}

Future<void> _initializeCoreServices() async {
  // تهيئة Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // تهيئة Supabase
  await SupabaseConfig.initialize();

  // تهيئة الإشعارات
  await FirebaseApi.initializeLocalNotifications();
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // تهيئة الإشعارات المحلية
  const AndroidInitializationSettings androidSettings =
      AndroidInitializationSettings('@mipmap/ic_launcher');
  await flutterLocalNotificationsPlugin.initialize(
    const InitializationSettings(android: androidSettings),
  );

  // تسجيل التبعيات
  Get.put(UserController(), permanent: true);
  Get.put(AuthService(), permanent: true);
  Get.put(DriverRepository(), permanent: true);
  
  // تهيئة Firebase API مع مستودع الرموز
  final tokenRepository = TokenRepository(SupabaseConfig.client);
  Get.put(FirebaseApi(tokenRepository), permanent: true);
}

void _setupConnectivityListener() {
  final Connectivity connectivity = Connectivity();
  connectivity.onConnectivityChanged.listen((List<ConnectivityResult> results) {
    if (results.contains(ConnectivityResult.none)) {
      Fluttertoast.showToast(
        msg: "تم فقدان الاتصال بالإنترنت",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
    } else {
      Fluttertoast.showToast(
        msg: "تم استعادة الاتصال بالإنترنت",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
    }
  });
}

class MyApp extends StatelessWidget {
  final bool isConnected;

  const MyApp({super.key, required this.isConnected});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'United-Accredited-Company-Driver-App',
      navigatorKey: navigatorKey,
      theme: _buildAppTheme(),
      locale: const Locale('ar'),
      initialRoute: AppRoutes.initial,
      getPages: AppRoutes.routes,
      home: isConnected ? const SplashScreen() : const NoInternetConnectionApp(),
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.0)),
            child: child!,
          ),
        );
      },
    );
  }

  ThemeData _buildAppTheme() {
    return ThemeData(
      primarySwatch: Colors.blue,
      fontFamily: 'Cairo',
      textTheme: const TextTheme(bodyMedium: TextStyle(fontSize: 16)),
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      colorScheme: ColorScheme.fromSwatch(
        primarySwatch: Colors.blue,
      ).copyWith(secondary: AppColors.primary),
    );
  }
}