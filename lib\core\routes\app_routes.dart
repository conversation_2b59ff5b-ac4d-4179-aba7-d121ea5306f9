import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart'; // أضف هذا الاستيراد
import 'package:driver_app/presentation/Pages/driver/Flight_tracking.dart';
import 'package:driver_app/presentation/Pages/driver/Dashboard.dart';
import 'package:driver_app/presentation/Pages/driver/auth/login_screen.dart';
import 'package:driver_app/presentation/Pages/driver/maintenance_request_srceen.dart';
import 'package:driver_app/presentation/Pages/driver/map.dart';
import '../../presentation/Pages/driver/Activity_logs.dart';
import '../../presentation/Pages/driver/profile_screen.dart';
// import '../../presentation/Pages/driver/settings_screen.dart';

class AppRoutes {
  static const String login = '/login';
  static const String dashboard = '/dashboard';
  static const String activityLog = '/Activity_log';
  static const String flightTracking = '/flight-tracking';
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String maintenance = '/maintenance';
  static const String mapScreen = '/MapScreen';

  static const String initial = dashboard;

  static List<GetPage> routes = [
    GetPage(
      name: mapScreen,
      page: () => MapScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: login,
      page: () => LoginPage(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: dashboard,
      page: () => Dashboard(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: activityLog,
      page: () {
        final args = Get.arguments as Map<String, dynamic>;
        return ActivityLogScreen(driverId: args['driverId']);
      },
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: profile,
      page: () => ProfileScreen(),
      transition: Transition.fadeIn,
    ),
    // GetPage(
    //   name: settings,
    //   page: () => const SettingsScreen(),
    //   transition: Transition.fadeIn,
    // ),
    GetPage(
      name: flightTracking,
      page: () {
        // final args = Get.arguments as Map<String, LatLng>;
        final args = Get.arguments as Map<String, LatLng>;

        return FlightTrackingScreen(
          tripId: args['tripId'] as int,
          taskId: args['taskId'] as String,
          pickupLocation: args['pickupLocation']!,
          dropoffLocation: args['dropoffLocation']!,
        );
      },
      transition: Transition.fadeIn,
    ),

    GetPage(
      name: maintenance,
      page: () => const AssignedVehiclePage(),
      transition: Transition.fadeIn,
    ),
  ];
}
