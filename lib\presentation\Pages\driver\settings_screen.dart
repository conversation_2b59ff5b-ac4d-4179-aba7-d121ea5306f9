// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:driver_app/core/responsive/responsive_helper.dart';

// class SettingsScreen extends StatelessWidget {
//   const SettingsScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(appBar: _buildAppBar(), body: _buildBody(context));
//   }

//   PreferredSizeWidget _buildAppBar() {
//     return AppBar(
//       title: Text(
//         'الإعدادات',
//         style: const TextStyle(fontWeight: FontWeight.bold),
//       ),
//       centerTitle: true,
//       leading: IconButton(
//         icon: Icon(Icons.arrow_forward),
//         onPressed: () => Get.back(),
//       ),
//       elevation: 0,
//     );
//   }

//   Widget _buildBody(BuildContext context) {
//     return Directionality(
//       textDirection: TextDirection.rtl,
//       child: SingleChildScrollView(
//         child: Padding(
//           padding: EdgeInsets.all(
//             ResponsiveHelper.getResponsivePadding(context),
//           ),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               _buildSectionTitle('الإعدادات العامة'),
//               const SizedBox(height: 16),
//               // Removed language section for Arabic-only UI
//               // _buildLanguageSection(),
//               //  _buildAboutSection(context),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildSectionTitle(String title) {
//     return Text(
//       title,
//       style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
//     );
//   }

  // Widget _buildLanguageSection() {
  //   return Card(
  //     elevation: 2,
  //     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
  //     child: Padding(
  //       padding: const EdgeInsets.all(16),
  //       child: Column(
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  //           Text(
  //             'Select Language',
  //             style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
  //           ),
  //           const SizedBox(height: 16),
  //           _buildLanguageButton('ar', 'العربية'),
  //           const SizedBox(height: 8),
  //           _buildLanguageButton('en', 'English'),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  // Widget _buildLanguageButton(String langCode, String label) {
  //   final isSelected = Get.locale?.languageCode == langCode;
  //   return SizedBox(
  //     width: double.infinity,
  //     child: ElevatedButton(
  //       onPressed: () => _handleLanguageChange(langCode),
  //       style: ElevatedButton.styleFrom(
  //         backgroundColor: isSelected ? Colors.blue : Colors.grey[200],
  //         foregroundColor: isSelected ? Colors.white : Colors.black87,
  //         padding: const EdgeInsets.symmetric(vertical: 12),
  //         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
  //       ),
  //       child: Text(label),
  //     ),
  //   );
  // }


  // Widget _buildAboutSection(BuildContext context) {
  //   return Card(
  //     elevation: 2,
  //     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
  //     child: Column(
  //       children: [
  //         _buildSettingsTile(
  //           icon: Icons.info,
  //           title: 'about_app'.tr,
  //           trailing: const Icon(Icons.arrow_forward_ios, size: 16),
  //           onTap: () => _showAboutDialog(context),
  //         ),
  //         const Divider(height: 1),
  //         _buildSettingsTile(
  //           icon: Icons.privacy_tip,
  //           title: 'privacy_policy'.tr,
  //           trailing: const Icon(Icons.arrow_forward_ios, size: 16),
  //           onTap: _showPrivacyPolicy,
  //         ),
  //         const Divider(height: 1),
  //         _buildSettingsTile(
  //           icon: Icons.help,
  //           title: 'help_support'.tr,
  //           trailing: const Icon(Icons.arrow_forward_ios, size: 16),
  //           onTap: _showHelpSupport,
  //         ),
  //       ],
  //     ),
  //   );
  // }



  // void _handleLocationServices(bool value) {
  //   // Implement location services logic
  // }

  // void _handleAutoSync(bool value) {
  //   // Implement auto sync logic
  // }




// }
