import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';
import 'package:driver_app/core/responsive/responsive_text.dart';

/// 🔍 Permission Diagnostic Widget
/// 
/// Use this widget to diagnose and test permission status
/// after adding the required permissions to AndroidManifest.xml and Info.plist
class PermissionDiagnosticWidget extends StatefulWidget {
  const PermissionDiagnosticWidget({super.key});

  @override
  State<PermissionDiagnosticWidget> createState() => _PermissionDiagnosticWidgetState();
}

class _PermissionDiagnosticWidgetState extends State<PermissionDiagnosticWidget> {
  Map<Permission, PermissionStatus> _permissionStatus = {};
  bool _isLoading = false;

  final List<Permission> _permissions = [
    Permission.camera,
    Permission.photos,
    Permission.storage,
    Permission.mediaLibrary,
  ];

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  Future<void> _checkPermissions() async {
    setState(() => _isLoading = true);
    
    Map<Permission, PermissionStatus> status = {};
    
    for (Permission permission in _permissions) {
      try {
        status[permission] = await permission.status;
      } catch (e) {
        status[permission] = PermissionStatus.denied;
      }
    }
    
    setState(() {
      _permissionStatus = status;
      _isLoading = false;
    });
  }

  Future<void> _requestPermission(Permission permission) async {
    setState(() => _isLoading = true);
    
    try {
      final status = await permission.request();
      _permissionStatus[permission] = status;
      
      if (status.isGranted) {
        Get.snackbar(
          'تم منح الإذن ✅',
          'إذن ${_getPermissionName(permission)} متاح الآن',
          backgroundColor: AppColors.success.withValues(alpha: 0.1),
          colorText: AppColors.success,
        );
      } else if (status.isDenied) {
        Get.snackbar(
          'تم رفض الإذن ❌',
          'إذن ${_getPermissionName(permission)} مطلوب للميزة',
          backgroundColor: AppColors.error.withValues(alpha: 0.1),
          colorText: AppColors.error,
        );
      } else if (status.isPermanentlyDenied) {
        Get.snackbar(
          'مطلوب تفعيل من الإعدادات 🔧',
          'يرجى تفعيل إذن ${_getPermissionName(permission)} من إعدادات التطبيق',
          backgroundColor: AppColors.error.withValues(alpha: 0.1),
          colorText: AppColors.error,
          mainButton: TextButton(
            onPressed: () => openAppSettings(),
            child: const Text('فتح الإعدادات'),
          ),
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ في طلب الإذن',
        'حدث خطأ: ${e.toString()}',
        backgroundColor: AppColors.error.withValues(alpha: 0.1),
        colorText: AppColors.error,
      );
    }
    
    setState(() => _isLoading = false);
  }

  String _getPermissionName(Permission permission) {
    switch (permission) {
      case Permission.camera:
        return 'الكاميرا';
      case Permission.photos:
        return 'معرض الصور';
      case Permission.storage:
        return 'التخزين';
      case Permission.mediaLibrary:
        return 'مكتبة الوسائط';
      default:
        return permission.toString();
    }
  }

  Color _getStatusColor(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return AppColors.success;
      case PermissionStatus.denied:
        return AppColors.error;
      case PermissionStatus.restricted:
        return AppColors.warning;
      case PermissionStatus.limited:
        return AppColors.accent;
      case PermissionStatus.permanentlyDenied:
        return AppColors.error;
      default:
        return AppColors.secondaryText;
    }
  }

  String _getStatusText(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'مُمنوح ✅';
      case PermissionStatus.denied:
        return 'مرفوض ❌';
      case PermissionStatus.restricted:
        return 'مقيد ⚠️';
      case PermissionStatus.limited:
        return 'محدود 🔒';
      case PermissionStatus.permanentlyDenied:
        return 'مرفوض نهائياً 🚫';
      default:
        return 'غير معروف ❓';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🔍 فحص الأذونات'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _checkPermissions,
          ),
        ],
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Padding(
          padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(context) * 1.5),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(
                    ResponsiveHelper.getResponsiveBorderRadius(context),
                  ),
                  border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      '🔧 فحص أذونات اختيار الصور',
                      baseFontSize: 20,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryText,
                      ),
                      textAlign: TextAlign.right,
                    ),
                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
                    ResponsiveText(
                      'استخدم هذه الشاشة للتأكد من تفعيل الأذونات المطلوبة',
                      baseFontSize: 14,
                      style: const TextStyle(
                        color: AppColors.secondaryText,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 2),
              
              // Permission Status List
              Expanded(
                child: _isLoading
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('جاري فحص الأذونات...'),
                          ],
                        ),
                      )
                    : ListView.separated(
                        itemCount: _permissions.length,
                        separatorBuilder: (context, index) => SizedBox(
                          height: ResponsiveHelper.getResponsiveSpacing(context),
                        ),
                        itemBuilder: (context, index) {
                          final permission = _permissions[index];
                          final status = _permissionStatus[permission] ?? PermissionStatus.denied;
                          
                          return Container(
                            padding: EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(context) * 1.2),
                            decoration: BoxDecoration(
                              color: AppColors.cardBackground,
                              borderRadius: BorderRadius.circular(
                                ResponsiveHelper.getResponsiveBorderRadius(context),
                              ),
                              border: Border.all(
                                color: _getStatusColor(status).withValues(alpha: 0.3),
                              ),
                            ),
                            child: Row(
                              children: [
                                // Permission Icon
                                Container(
                                  padding: EdgeInsets.all(
                                    ResponsiveHelper.getResponsiveSpacing(context) * 0.8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _getStatusColor(status).withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(
                                      ResponsiveHelper.getResponsiveBorderRadius(context) * 0.8,
                                    ),
                                  ),
                                  child: Icon(
                                    _getPermissionIcon(permission),
                                    color: _getStatusColor(status),
                                    size: ResponsiveHelper.getResponsiveIconSize(context),
                                  ),
                                ),
                                
                                SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context)),
                                
                                // Permission Details
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      ResponsiveText(
                                        _getPermissionName(permission),
                                        baseFontSize: 16,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w600,
                                          color: AppColors.primaryText,
                                        ),
                                        textAlign: TextAlign.right,
                                      ),
                                      SizedBox(height: 4),
                                      ResponsiveText(
                                        _getStatusText(status),
                                        baseFontSize: 14,
                                        style: TextStyle(
                                          color: _getStatusColor(status),
                                          fontWeight: FontWeight.w500,
                                        ),
                                        textAlign: TextAlign.right,
                                      ),
                                    ],
                                  ),
                                ),
                                
                                // Action Button
                                if (!status.isGranted) ...[
                                  SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context)),
                                  ElevatedButton(
                                    onPressed: status.isPermanentlyDenied
                                        ? () => openAppSettings()
                                        : () => _requestPermission(permission),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: status.isPermanentlyDenied
                                          ? AppColors.warning
                                          : AppColors.primary,
                                      foregroundColor: AppColors.white,
                                      padding: EdgeInsets.symmetric(
                                        horizontal: ResponsiveHelper.getResponsiveSpacing(context),
                                        vertical: ResponsiveHelper.getResponsiveSpacing(context) * 0.5,
                                      ),
                                    ),
                                    child: Text(
                                      status.isPermanentlyDenied ? 'الإعدادات' : 'طلب',
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          );
                        },
                      ),
              ),
              
              // Instructions
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(context) * 1.2),
                decoration: BoxDecoration(
                  color: AppColors.accent.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(
                    ResponsiveHelper.getResponsiveBorderRadius(context),
                  ),
                  border: Border.all(color: AppColors.accent.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      '💡 تعليمات:',
                      baseFontSize: 16,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        color: AppColors.accent,
                      ),
                      textAlign: TextAlign.right,
                    ),
                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
                    ResponsiveText(
                      '• إذا كانت جميع الأذونات "مرفوضة"، تأكد من إعادة بناء التطبيق\n'
                      '• اضغط "طلب" لطلب الإذن من النظام\n'
                      '• إذا كان الإذن "مرفوض نهائياً"، اضغط "الإعدادات" لتفعيله يدوياً\n'
                      '• للكاميرا والمعرض، يجب أن يكون الوضع "مُمنوح ✅"',
                      baseFontSize: 14,
                      style: const TextStyle(
                        color: AppColors.accent,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getPermissionIcon(Permission permission) {
    switch (permission) {
      case Permission.camera:
        return Icons.camera_alt;
      case Permission.photos:
        return Icons.photo_library;
      case Permission.storage:
        return Icons.storage;
      case Permission.mediaLibrary:
        return Icons.perm_media;
      default:
        return Icons.security;
    }
  }
}