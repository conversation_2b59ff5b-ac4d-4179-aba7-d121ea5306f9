// lib/controllers/user_controller.dart
import 'package:get/get.dart';
import 'package:driver_app/Data/models/user.dart';

class UserController extends GetxController {
  Rx<AppUser?> user = Rx<AppUser?>(null);

  void setUser(AppUser newUser) {
    user.value = newUser;
  }

  AppUser? get currentUser => user.value;
  Future<void> signOut() async {
    user.value = null;
    // يمكن إضافة أي تنظيف آخر متعلق بالمستخدم هنا
  }
}
