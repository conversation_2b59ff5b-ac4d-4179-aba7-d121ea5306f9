import 'package:flutter/material.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/theme/app_styles.dart';

void showTaskStartedConfirmationDialog(BuildContext context, VoidCallback onConfirm) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppStyles.getSpacing(context) / 2),
        ),
        child: Padding(
          padding: EdgeInsets.all(AppStyles.getSpacing(context)),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.all(AppStyles.getSpacing(context) / 2),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.warning,
                    width: 4,
                  ),
                ),
                child: Icon(
                  Icons.priority_high,
                  size: 48,
                  color: AppColors.warning,
                ),
              ),
              SizedBox(height: AppStyles.getSpacing(context)),

              Text(
                'تأكيد بدء المهمة',
                textAlign: TextAlign.center,
                style: AppStyles.getSubheadingStyle(context).copyWith(
                  color: AppColors.primary,
                ),
              ),
              SizedBox(height: AppStyles.getSpacing(context)),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      onConfirm();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                    ),
                    child: Text('تأكيد', style: TextStyle(fontSize: 16)),
                  ),
                  OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text('إلغاء', style: TextStyle(fontSize: 16)),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    },
  );
}