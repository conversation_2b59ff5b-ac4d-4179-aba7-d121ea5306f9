
import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:driver_app/Data/models/user.dart';
import 'package:driver_app/core/controller/user_controller.dart';
import 'package:driver_app/core/routes/app_routes.dart';
import 'package:driver_app/core/routes/supabase.dart';
import 'package:driver_app/core/services/firebase/firebase_api.dart';
import 'package:driver_app/presentation/Pages/driver/auth/reset_password_screen.dart';


class AuthService extends GetxController {
  final supabase = SupabaseConfig.client;
  final UserController userController = Get.find<UserController>();
  var currentUser = Rxn<AppUser>();
  DateTime? lastSessionTime;

  @override
  void onInit() {
    super.onInit();
    _checkAutoLogin();
  }
  

  Future<void> _checkAutoLogin() async {
    final prefs = await SharedPreferences.getInstance();
    final rememberMe = prefs.getBool('rememberMe') ?? false;
    final sessionExpired = await _isSessionExpired();

    if (rememberMe && !sessionExpired) {
      final email = prefs.getString('savedEmail');
      final password = prefs.getString('savedPassword');

      if (email != null && password != null) {
        await signInDriver(email, password, rememberMe: true);
      }
    } else if (sessionExpired) {
      await signOut();
    }
  }

  Future<bool> _isSessionExpired() async {
    final prefs = await SharedPreferences.getInstance();
    final lastLogin = prefs.getString('lastLoginTime');

    if (lastLogin == null) return true;

    final lastTime = DateTime.parse(lastLogin);
    return DateTime.now().difference(lastTime).inHours > 24;
  }

  Future<void> _logActivity(String action, String description) async {
    try {
      final user = userController.user.value;
      if (user == null || user.driverDetails == null) return;

      // الحصول على user_id من جدول السائقين
      final driverResponse =
          await supabase
              .from('drivers')
              .select('user_id')
              .eq('id', user.driverDetails!.id)
              .single();

      final userId = driverResponse['user_id'];
      final fullDescription = '${user.fullName} - $description';

      await supabase.from('activity_logs').insert({
        'user_id': userId,
        'action': action,
        'description': fullDescription,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('فشل في تسجيل النشاط: $e');
    }
  }

  Future<void> signInDriver(
    String email,
    String password, {
    bool rememberMe = false,
  }) async {
    try {
      final response = await supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      final userData =
          await supabase
              .from('users')
              .select('*, roles(name), drivers(*)')
              .eq('auth_user_id', response.user!.id)
              .single();

      if (userData['roles']['name'] != 'Driver') {
        throw Exception('ليس لديك صلاحية الدخول كنظام سائق');
      }

      final driversList = userData['drivers'];
      final driverJson =
          (driversList is List && driversList.isNotEmpty)
              ? driversList.first as Map<String, dynamic>
              : null;

      final driver = AppUser(
        id: userData['id'].toString(),
        email: userData['email'] ?? '',
        fullName: userData['full_name'] ?? '',
        role: userData['roles']['name'] ?? '',
        driverDetails: DriverDetails.fromJson(driverJson),
      );

      userController.setUser(driver);
      try {
         if (Get.isRegistered<FirebaseApi>()) {
      final firebaseApi = Get.find<FirebaseApi>();
      await firebaseApi.saveTokenForCurrentUser();
      print('==========================================================');
      print(firebaseApi);
      print('==========================================================');

    } else {
      print('⚠️ FirebaseApi not registered yet');
    }
      } catch (e) {
        print('⚠️ Error saving FCM token: $e');
      }
 
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('lastLoginTime', DateTime.now().toString());
      await prefs.setBool('rememberMe', rememberMe);

      if (rememberMe) {
        await prefs.setString('savedEmail', email);
        await prefs.setString('savedPassword', password);
      } else {
        await prefs.remove('savedEmail');
        await prefs.remove('savedPassword');
      }

      // تحديث حالة السائق وتسجيل النشاط
      await updateDriverStatus('active');
      await _logActivity('login', 'تسجيل دخول السائق');
      print('✅ تم تعيين المستخدم: ${driver.email}, role: ${driver.role}, driverID: ${driver.driverDetails?.id}');
      print('✅ من داخل UserController: ${userController.user.value?.email}');

      // Navigation will be handled by the calling screen after success dialog
    } catch (e) {
      Get.snackbar('خطأ في تسجيل الدخول', e.toString());
      print('SignIn Error: $e');
    }
  }

  Future<void> updateDriverStatus(String status) async {
    try {
      final user = userController.user.value;
      if (user == null || user.driverDetails == null) {
        throw Exception('بيانات السائق غير متوفرة');
      }

      await supabase
          .from('drivers')
          .update({'status': status})
          .eq('id', user.driverDetails!.id);

      // تحديث بيانات السائق المحلية
      userController.user.value = user.copyWith(
        driverDetails: user.driverDetails!.copyWith(status: status),
      );
      userController.user.refresh();

      print('تم تحديث حالة السائق إلى: $status');
    } catch (e) {
      print('خطأ في تحديث حالة السائق: $e');
      throw Exception('فشل في تحديث حالة السائق');
    }
  }

  Future<bool> checkAuthStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final rememberMe = prefs.getBool('rememberMe') ?? false;
    final session = supabase.auth.currentSession;

    return rememberMe && session != null && !(await _isSessionExpired());
  }

  Future<void> signOut() async {
    try {
      // تسجيل نشاط الخروج وتحديث الحالة
      await _logActivity('logout', 'تسجيل خروج السائق');
      await updateDriverStatus('not active');

      await supabase.auth.signOut();

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('lastLoginTime');
      await prefs.remove('savedEmail');
      await prefs.remove('savedPassword');
      await prefs.setBool('rememberMe', false);

      await userController.signOut();
      Get.offAllNamed(AppRoutes.login);
    } catch (e) {
      Get.snackbar('خطأ في تسجيل الخروج', e.toString());
      print('SignOut error: $e');
    }
  }
  //================================================================================================
  //================================================================================================
  //================================================================================================

  static Future<void> requestResetPassword(String email, BuildContext context) async {
    try {
      await SupabaseConfig.client.auth.resetPasswordForEmail(
        email,
        redirectTo: 'devcode://password-reset',
      );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم إرسال رابط إعادة التعيين إلى بريدك الإلكتروني')),
      );
    } catch (e) {

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل إرسال رابط إعادة التعيين: ${e.toString()}')),
      );
    }
  }

  static void configDeepLink(BuildContext context) {
    final appLinks = AppLinks();

    appLinks.uriLinkStream.listen((uri) {
      if (uri.host == 'password-reset') {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => ResetPasswordScreen()),
          (route) => false,
        );
      }
    });
  }

  static Future<void> resetPassword(String newPassword) async {
    await SupabaseConfig.client.auth.updateUser(
      UserAttributes(password: newPassword),
    );
    Get.offAllNamed('/login');
  }
}
