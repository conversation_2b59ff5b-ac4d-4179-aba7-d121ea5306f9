import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:driver_app/core/controller/user_controller.dart';
import 'package:driver_app/core/responsive/driver_cl.dart';
import 'package:driver_app/core/services/auth_service.dart';
import 'package:driver_app/presentation/Pages/driver/Flight_tracking.dart';
import '../../../../Data/models/task.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/responsive/responsive_helper.dart';

class TaskCard extends StatelessWidget {
  final TaskModel task;

  const TaskCard({super.key, required this.task});

  Color getCardColor(String status) {
    switch (status) {
      case 'completed':
        return AppColors.statusCompleted.withOpacity(0.08);
      case 'in_progress':
        return AppColors.statusInProgress.withOpacity(0.08);
      case 'pending':
        return AppColors.statusNew.withOpacity(0.08);
      default:
        return AppColors.cardBackground;
    }
  }

  Color getStatusDotColor(String status) {
    switch (status) {
      case 'completed':
        return AppColors.statusCompleted;
      case 'in_progress':
        return AppColors.statusInProgress;
      case 'pending':
        return AppColors.statusNew;
      default:
        return AppColors.statusInactive;
    }
  }

  @override
  Widget build(BuildContext context) {
    final double padding = ResponsiveHelper.getResponsivePadding(context);
    final double borderRadius = ResponsiveHelper.getResponsiveBorderRadius(context);
    final double fontSize = ResponsiveHelper.getResponsiveFontSize(context, 14);
    final double titleFontSize = ResponsiveHelper.getResponsiveFontSize(context, 16);
    final cardColor = getCardColor(task.status);
    final statusDotColor = getStatusDotColor(task.status);
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: EdgeInsetsDirectional.symmetric(vertical: padding * 0.5, horizontal: padding * 0.5),
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: cardColor,
        border: Border.all(color: AppColors.borderColor),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.dividerColor.withOpacity(0.08),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: () => _showTripDetails(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Header with status dot, Task Name, and Status Badge
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Status Dot
                  Container(
                    width: 12,
                    height: 12,
                    margin: const EdgeInsetsDirectional.only(start: 2, end: 8),
                    decoration: BoxDecoration(
                      color: statusDotColor,
                      shape: BoxShape.circle,
                      border: Border.all(color: statusDotColor, width: 2),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      task.title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: titleFontSize,
                        color: AppColors.primaryText,
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.right,
                    ),
                  ),
                  _buildStatusBadge(context, fontSize),
                ],
              ),
              const SizedBox(height: 8),
              // Execution Date
              _buildDetailRow(
                Icons.calendar_today,
                'اخر مهلة التنفيذ:',
                task.dueDate != null ? DateFormat('yyyy-MM-dd').format(task.dueDate!) : 'لا يوجد تاريخ',
                fontSize,
              ),
              const SizedBox(height: 8),
              // Pickup Location
              _buildDetailRow(
                Icons.location_on,
                'موقع الأستلام :',
                task.pickupLocation.name,
                fontSize,
                maxLines: 1,
              ),
              const SizedBox(height: 12),
              // Buttons Row
              Row(
                children: [
                  Expanded(
                    child: FilledButton(
                      style: FilledButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: AppColors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: EdgeInsets.symmetric(vertical: padding * 0.8),
                        elevation: 0,
                      ),
                      onPressed: () => _startTask(context),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Tooltip(
                            message: 'بدء المهمة',
                            child: Icon(Icons.play_arrow, size: fontSize, color: AppColors.white),
                          ),
                          const SizedBox(width: 6),
                          Text('بدء المهمة', style: TextStyle(fontSize: fontSize, color: AppColors.white, fontWeight: FontWeight.bold)),
                        ],
                      ),
                    ),
                  ),
                 
                  SizedBox(width: padding),
                   Expanded(
                    child: FilledButton.tonal(
                      style: FilledButton.styleFrom(
                        backgroundColor: AppColors.secondary,
                        foregroundColor: AppColors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: EdgeInsets.symmetric(vertical: padding * 0.8),
                        elevation: 0,
                      ),
                      onPressed: () => _showTripDetails(context),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Tooltip(
                            message: 'تفاصيل المهمة',
                            child: Icon(Icons.info_outline, size: fontSize, color: AppColors.white),
                          ),
                          const SizedBox(width: 6),
                          Text('تفاصيل المهمة', style: TextStyle(fontSize: fontSize, color: AppColors.white)),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(BuildContext context, double fontSize) {
    final color = getStatusDotColor(task.status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getStatusIcon(task.status), color: color, size: fontSize),
          const SizedBox(width: 4),
          Text(
            'الحالة: ${_getStatusLabel(task.status)}',
            style: TextStyle(
              color: color,
              fontSize: fontSize - 1,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.right,
          ),
        ],
      ),
    );
  }

  String _getStatusLabel(String status) {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'pending':
        return 'قيد الانتظار';
      default:
        return status;
    }
  }

  Widget _buildDetailRow(
    IconData icon,
    String label,
    String value,
    double fontSize, {
    int maxLines = 2,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: fontSize, color: AppColors.secondaryIconColor),
        const SizedBox(width: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: fontSize,
            color: AppColors.secondaryText,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.right,
        ),
         Text(
          value,
          style: TextStyle(
            fontSize: fontSize,
            color: AppColors.primaryText,
            fontWeight: FontWeight.w600,
          ),
          maxLines: maxLines,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.right,
        ),
      ],
    );
  }

  Future<void> _startTask(BuildContext context) async {
    final driverRepo = Get.find<DriverRepository>();
    final userController = Get.find<UserController>();
    final driver = userController.currentUser?.driverDetails;

    if (driver == null) {
      Get.snackbar('خطأ', 'السائق غير موجود');
      return;
    }

    try {
      final tripId = await driverRepo.createNewTrip(
        driverId: int.parse(driver.id),
        taskId: int.parse(task.id),
        startLoc: task.pickupLocation.coordinates,
        endLoc: task.dropoffLocation.coordinates,
      );

      final authService = Get.find<AuthService>();
      await authService.updateDriverStatus('working');
      _logActivity('start the trip', 'started the trip');

      Get.to(
        () => FlightTrackingScreen(
          tripId: tripId,
          taskId: task.id,
          pickupLocation: task.pickupLocation.coordinates,
          dropoffLocation: task.dropoffLocation.coordinates,
        ),
      );
    } catch (e) {
      Get.snackbar('خطأ', e.toString());
    }
  }

  void _showTripDetails(BuildContext context) {
    Get.bottomSheet(
      Container(
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 40,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Task Title
              Text(
                task.title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(height: 8),

              // Task Description
              if (task.description != null && task.description!.isNotEmpty)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      task.description!,
                      style: const TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),

              // Task Details
              _buildDetailItem(Icons.assignment, 'معرف المهمة', task.id),
              _buildDetailItem(
                Icons.calendar_today,
                'تاريخ التنفيذ',
                task.dueDate != null
                    ? DateFormat('yyyy-MM-dd – HH:mm').format(task.dueDate!)
                    : 'لا يوجد تاريخ',
              ),
              _buildDetailItem(
                Icons.location_on,
                'موقع الاستلام',
                '${task.pickupLocation.name}\n(${task.pickupLocation.coordinates.latitude}, ${task.pickupLocation.coordinates.longitude})',
              ),
              _buildDetailItem(
                Icons.location_on,
                'موقع التسليم',
                '${task.dropoffLocation.name}\n(${task.dropoffLocation.coordinates.latitude}, ${task.dropoffLocation.coordinates.longitude})',
              ),
              _buildDetailItem(
                Icons.star,
                'الحالة',
                task.status,
                valueColor: _getStatusColor(task.status),
              ),

              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: const EdgeInsets.symmetric(vertical: 15),
                  ),
                  onPressed: () => Get.back(),
                  child: Text(
                    'إغلاق',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(
    IconData icon,
    String label,
    String value, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: AppColors.primary),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    color: valueColor ?? Colors.black,
                    fontWeight: valueColor != null ? FontWeight.bold : null,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'completed':
        return Icons.check_circle;
      case 'in_progress':
        return Icons.timelapse;
      default:
        return Icons.pending;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'in_progress':
        return Colors.blue;
      default:
        return Colors.orange;
    }
  }

  Future<void> _logActivity(String action, String description) async {
    try {
      final supabase = Supabase.instance.client;
      final userController = Get.find<UserController>();
      final user = userController.user.value;
      if (user == null || user.driverDetails == null) return;

      final driverResponse =
          await supabase
              .from('drivers')
              .select('user_id')
              .eq('id', user.driverDetails!.id)
              .single();

      final userId = driverResponse['user_id'];
      final fullDescription = '${user.fullName} - $description';

      await supabase.from('activity_logs').insert({
        'user_id': userId,
        'action': action,
        'description': fullDescription,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Failed to log activity: $e');
    }
  }
}
