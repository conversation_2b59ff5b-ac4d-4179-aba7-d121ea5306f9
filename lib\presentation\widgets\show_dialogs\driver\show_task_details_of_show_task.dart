import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../showDialogs/driver/show_task_start_confirmation.dart';
void showTaskDetailsBottomSheetOfShowTask(BuildContext context) {
  showModalBottomSheet(
    context: context,
    builder: (context) {
      return Directionality(
        textDirection: TextDirection.rtl,
        child: Padding(
          padding: EdgeInsets.all(30),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Text(
                    'تفاصيل المهمة',
                    style: _titleTextStyle(),
                  ),
                ),
                Divider(),
                _buildDetailRow('اسم المهمة :', 'توصيل البضائع'),
                _buildDetailRow('الموقع :', 'تفاصيل الموقع'),
                _buildDetailRow('تاريخ التنفيذ :', '12 يناير 2025'),
                _buildDetailRow('وقت التنفيذ :', '5:00 م'),
                _buildDetailRow('آخر موعد للتنفيذ :', '5:15 م'),
                _buildDetailRow('موعد التسليم النهائي :', '13 يناير 2025'),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 5),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('مزيد من التفاصيل:', style: _boldTextStyle()),
                      const SizedBox(height: 5),
                      Text('لوريم إيبسوم', textAlign: TextAlign.right),
                    ],
                  ),
                ),
                SizedBox(height: 10),
                Center(
                  child: ElevatedButton(
                    onPressed: () {
                      showTaskStartedConfirmationDialog(context, () {
                        Navigator.pop(context);
                        Get.toNamed('/flightTracking');
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 5,
                        horizontal: 80,
                      ),
                      child: Text(
                        'ابدأ',
                        style: TextStyle(color: Colors.white,fontSize: 16),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}

Widget _buildDetailRow(String title, String value) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 5),
    child: Row(
      children: [
        Text(title, style: _boldTextStyle()),
        SizedBox(width: 5),
        Flexible(child: Text(value, textAlign: TextAlign.start)),
      ],
    ),
  );
}

TextStyle _titleTextStyle() {
  return TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: Colors.blue,
  );
}

TextStyle _boldTextStyle() {
  return TextStyle(fontWeight: FontWeight.bold);
}
