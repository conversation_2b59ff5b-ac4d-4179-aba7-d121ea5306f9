import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/routes/app_routes.dart';
import 'package:driver_app/core/services/auth_service.dart';
import 'package:lottie/lottie.dart'; // لإضافة رسوم متحركة

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  final AuthService _authService = Get.find<AuthService>();

  @override
  void initState() {
    super.initState();

    // تهيئة المتحكم للرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 1500),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _animationController.forward();
    _navigateToNextScreen();
  }

  Future<void> _navigateToNextScreen() async {
    // انتظار انتهاء الرسوم المتحركة + تأخير إضافي
    await Future.delayed(Duration(seconds: 3));

    final isLoggedIn = await _authService.checkAuthStatus();

    Get.offAllNamed(isLoggedIn ? AppRoutes.dashboard : AppRoutes.login);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // خيار 1: استخدام Lottie للرسوم المتحركة
            Lottie.asset(
              'assets/animations/splash_animation.json',
              width: 200,
              height: 200,
              fit: BoxFit.contain,
            ),

            // أو خيار 2: استخدام FadeTransition مع الصورة
            /*
            FadeTransition(
              opacity: _animation,
              child: Image.asset(
                'assets/images/logo.png',
                width: 200,
                height: 200,
              ),
            ),
            */
            SizedBox(height: 20),
            AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _animation.value * 1 + 1, // تأثير تكبير
                  child: Opacity(
                    opacity: _animation.value,
                    child: Text(
                      'تطبيق السائق',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
