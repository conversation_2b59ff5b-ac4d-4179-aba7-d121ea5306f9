import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:driver_app/core/services/auth_service.dart';
import 'package:driver_app/presentation/Pages/driver/auth/login_screen.dart';

class ProfileController extends GetxController {
  // Image picker instance
  final ImagePicker _picker = ImagePicker();
  
  // Selected profile image
  Rx<XFile?> selectedProfileImage = Rx<XFile?>(null);
  
  // Loading states
  RxBool isImageLoading = false.obs;
  RxBool isLogoutLoading = false.obs;

  // Auth service
  final AuthService _authService = Get.find<AuthService>();

  /// Pick image from camera with permission handling
  Future<void> pickImageFromCamera() async {
    try {
      isImageLoading.value = true;
      
      // Check camera permission first
      final cameraStatus = await Permission.camera.status;
      if (cameraStatus.isDenied) {
        final result = await Permission.camera.request();
        if (result.isDenied) {
          _showPermissionDeniedMessage('الكاميرا', 'لالتقاط الصور');
          return;
        }
        if (result.isPermanentlyDenied) {
          _showPermissionPermanentlyDeniedMessage('الكاميرا');
          return;
        }
      }
      
      // Attempt to pick image from camera
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 800,
        maxHeight: 800,
        preferredCameraDevice: CameraDevice.rear,
      );
      
      // Handle the result
      if (image != null) {
        selectedProfileImage.value = image;
        Get.back(); // Close bottom sheet
        _showSuccessMessage('تم التقاط الصورة بنجاح', 'تم حفظ الصورة الجديدة');
      } else {
        // User cancelled - this is normal behavior, just close bottom sheet
        Get.back();
        _showInfoMessage('تم الإلغاء', 'لم يتم التقاط أي صورة');
      }
      
    } on PlatformException catch (e) {
      Get.back(); // Close bottom sheet
      _handlePlatformException(e, 'التقاط الصورة');
    } catch (e) {
      Get.back(); // Close bottom sheet
      _showErrorMessage('خطأ غير متوقع', 'حدث خطأ أثناء التقاط الصورة: ${e.toString()}');
    } finally {
      isImageLoading.value = false;
    }
  }

  /// Pick image from gallery with permission handling
  Future<void> pickImageFromGallery() async {
    try {
      isImageLoading.value = true;
      
      // Check photos/storage permission
      PermissionStatus photosStatus;
      if (GetPlatform.isIOS) {
        photosStatus = await Permission.photos.status;
      } else {
        // For Android, check storage permission
        photosStatus = await Permission.storage.status;
        if (photosStatus.isDenied) {
          // Try media library permission for newer Android versions
          photosStatus = await Permission.mediaLibrary.status;
        }
      }
      
      if (photosStatus.isDenied) {
        final Permission permission = GetPlatform.isIOS ? Permission.photos : Permission.storage;
        final result = await permission.request();
        if (result.isDenied) {
          _showPermissionDeniedMessage('معرض الصور', 'لاختيار الصور');
          return;
        }
        if (result.isPermanentlyDenied) {
          _showPermissionPermanentlyDeniedMessage('معرض الصور');
          return;
        }
      }
      
      // Attempt to pick image from gallery
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 800,
        maxHeight: 800,
      );
      
      // Handle the result
      if (image != null) {
        selectedProfileImage.value = image;
        Get.back(); // Close bottom sheet
        _showSuccessMessage('تم اختيار الصورة بنجاح', 'تم حفظ الصورة الجديدة');
      } else {
        // User cancelled - this is normal behavior, just close bottom sheet
        Get.back();
        _showInfoMessage('تم الإلغاء', 'لم يتم اختيار أي صورة');
      }
      
    } on PlatformException catch (e) {
      Get.back(); // Close bottom sheet
      _handlePlatformException(e, 'اختيار الصورة');
    } catch (e) {
      Get.back(); // Close bottom sheet
      _showErrorMessage('خطأ غير متوقع', 'حدث خطأ أثناء اختيار الصورة: ${e.toString()}');
    } finally {
      isImageLoading.value = false;
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      isLogoutLoading.value = true;
      
      // Sign out from auth service
      await _authService.signOut();
      
      // Navigate to login screen as requested in the prompt
      Get.offAll(() => const LoginPage());
      
      // Show success message
      Get.snackbar(
        'تم تسجيل الخروج',
        'تم تسجيل الخروج بنجاح',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Get.theme.colorScheme.primary.withValues(alpha: 0.1),
        colorText: Get.theme.colorScheme.primary,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تسجيل الخروج: ${e.toString()}',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Get.theme.colorScheme.error.withValues(alpha: 0.1),
        colorText: Get.theme.colorScheme.error,
      );
    } finally {
      isLogoutLoading.value = false;
    }
  }

  /// Reset selected image
  void clearSelectedImage() {
    selectedProfileImage.value = null;
    _showInfoMessage('تم مسح الصورة', 'تم إزالة صورة الملف الشخصي');
  }

  // Helper methods for consistent user feedback
  void _showSuccessMessage(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Get.theme.colorScheme.primary.withValues(alpha: 0.1),
      colorText: Get.theme.colorScheme.primary,
      icon: Icon(
        Icons.check_circle_outline,
        color: Get.theme.colorScheme.primary,
      ),
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  void _showErrorMessage(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Get.theme.colorScheme.error.withValues(alpha: 0.1),
      colorText: Get.theme.colorScheme.error,
      icon: Icon(
        Icons.error_outline,
        color: Get.theme.colorScheme.error,
      ),
      duration: const Duration(seconds: 4),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  void _showInfoMessage(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Get.theme.colorScheme.secondary.withValues(alpha: 0.1),
      colorText: Get.theme.colorScheme.secondary,
      icon: Icon(
        Icons.info_outline,
        color: Get.theme.colorScheme.secondary,
      ),
      duration: const Duration(seconds: 2),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  void _showPermissionDeniedMessage(String permissionName, String purpose) {
    Get.snackbar(
      'تم رفض الإذن',
      'يحتاج التطبيق للوصول إلى $permissionName $purpose',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Get.theme.colorScheme.error.withValues(alpha: 0.1),
      colorText: Get.theme.colorScheme.error,
      icon: Icon(
        Icons.warning_amber_outlined,
        color: Get.theme.colorScheme.error,
      ),
      duration: const Duration(seconds: 4),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  void _showPermissionPermanentlyDeniedMessage(String permissionName) {
    Get.snackbar(
      'مطلوب تفعيل الإذن',
      'يرجى تفعيل إذن $permissionName من إعدادات التطبيق',
      snackPosition: SnackPosition.TOP,
      backgroundColor: Get.theme.colorScheme.error.withValues(alpha: 0.1),
      colorText: Get.theme.colorScheme.error,
      icon: Icon(
        Icons.settings,
        color: Get.theme.colorScheme.error,
      ),
      duration: const Duration(seconds: 5),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      mainButton: TextButton(
        onPressed: () => openAppSettings(),
        child: Text(
          'فتح الإعدادات',
          style: TextStyle(color: Get.theme.colorScheme.error),
        ),
      ),
    );
  }

  void _handlePlatformException(PlatformException e, String operation) {
    String userMessage;
    switch (e.code) {
      case 'camera_access_denied':
        userMessage = 'تم رفض الوصول للكاميرا. يرجى السماح بالوصول للكاميرا من الإعدادات.';
        break;
      case 'photo_access_denied':
        userMessage = 'تم رفض الوصول لمعرض الصور. يرجى السماح بالوصول للصور من الإعدادات.';
        break;
      case 'invalid_image':
        userMessage = 'الصورة المختارة غير صالحة. يرجى اختيار صورة أخرى.';
        break;
      case 'file_not_found':
        userMessage = 'لم يتم العثور على الملف. يرجى المحاولة مرة أخرى.';
        break;
      default:
        userMessage = 'حدث خطأ أثناء $operation. يرجى المحاولة مرة أخرى.';
        break;
    }
    
    _showErrorMessage('خطأ في النظام', userMessage);
  }

  @override
  void onClose() {
    selectedProfileImage.close();
    isImageLoading.close();
    isLogoutLoading.close();
    super.onClose();
  }
}