import 'package:get/get_state_manager/src/rx_flutter/rx_disposable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:driver_app/Data/models/location_model.dart';
import 'package:driver_app/Data/models/task.dart';
// import 'package:driver_app/Data/models/trip_model.dart';

class DriverRepository extends GetxService {
  final SupabaseClient _supabase = Supabase.instance.client;
  Future<int> createNewTrip({
    required int driverId,
    required int taskId,
    required LatLng startLoc,
    required LatLng endLoc,
  }) async {
    final vs =
        await _supabase
            .from('vehicle_assignments')
            .select('vehicle_id')
            .eq('driver_id', driverId)
            .eq('is_active', true)
            .single();
    final response =
        await _supabase
            .from('trips')
            .insert({
              'driver_id': driverId,
              'task_id': taskId,
              'start_time': DateTime.now().toIso8601String(),
              'vehicle_id': vs['vehicle_id'],
              'start_location': startLoc.toJson(),
              'end_location': endLoc.toJson(),
            })
            .select('id')
            .single();
    await _supabase
        .from('tasks')
        .update({'status': 'in_progress'})
        .eq('id', taskId);

    return response['id'] as int; // إرجاع tripId
  }

  // Future<int> _getActiveVehicleId(int driverId) async {
  //   final response =
  //       await _supabase
  //           .from('vehicle_assignments')
  //           .select('vehicle_id')
  //           .eq('driver_id', driverId)
  //           .eq('is_active', true)
  //           .single();

  //   return response['vehicle_id'] as int;
  // }

  Future<List<TaskModel>> getDriverTasks(String driverId) async {
    try {
      final tasksResponse = await _supabase
          .from('tasks')
          .select('''
            id,
            title,
            description,
            status,
            due_date,
            created_at,
            pickup_location:pickup_location_id (latitude, longitude),
            dropoff_location:dropoff_location_id (id, name, latitude, longitude)
          ''')
          .eq('assigned_to', driverId)
          .eq('status', 'pending');

      return tasksResponse.map<TaskModel>((task) {
        return TaskModel(
          id: task['id'].toString(),
          title: task['title'] ?? 'No Title',
          description: task['description'],
          status: task['status'] ?? 'pending',
          dueDate:
              task['due_date'] != null
                  ? DateTime.parse(task['due_date'])
                  : null,
          createdAt:
              task['created_at'] != null
                  ? DateTime.parse(task['created_at'])
                  : null,
          pickupLocation: LocationModel(
            id: 0,
            name: 'Pickup',
            coordinates: LatLng(
              (task['pickup_location']?['latitude'] as num?)?.toDouble() ?? 0.0,
              (task['pickup_location']?['longitude'] as num?)?.toDouble() ??
                  0.0,
            ),
          ),
          dropoffLocation: LocationModel(
            id:
                task['dropoff_location']?['id'] != null
                    ? int.tryParse(
                          task['dropoff_location']!['id'].toString(),
                        ) ??
                        0
                    : 0,
            name: task['dropoff_location']?['name'] ?? 'Unknown',
            coordinates: LatLng(
              (task['dropoff_location']?['latitude'] as num?)?.toDouble() ??
                  0.0,
              (task['dropoff_location']?['longitude'] as num?)?.toDouble() ??
                  0.0,
            ),
          ),
        );
      }).toList();
    } catch (e) {
      throw Exception('فشل في تحميل المهام: ${e.toString()}');
    }
  }
}
