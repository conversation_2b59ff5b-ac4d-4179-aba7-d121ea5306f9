import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/controller/profile_controller.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';
import 'package:driver_app/core/responsive/responsive_text.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/theme/app_styles.dart';

class LogoutConfirmationDialog extends StatelessWidget {
  const LogoutConfirmationDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final ProfileController profileController = Get.find<ProfileController>();
    
    return Directionality(
      textDirection: TextDirection.rtl,
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            ResponsiveHelper.getResponsiveBorderRadius(context),
          ),
        ),
        backgroundColor: AppColors.cardBackground,
        title: Row(
          children: [
            Icon(
              Icons.logout,
              color: AppColors.error,
              size: ResponsiveHelper.getResponsiveIconSize(context),
            ),
            SizedBox(
              width: ResponsiveHelper.getResponsiveSpacing(context),
            ),
            Expanded(
              child: ResponsiveText(
                'تأكيد تسجيل الخروج',
                baseFontSize: ResponsiveHelper.getResponsiveFontSize(context, 18),
                style: AppStyles.getHeadingStyle(context).copyWith(
                  color: AppColors.primaryText,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.right,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ResponsiveText(
              'هل أنت متأكد أنك تريد تسجيل الخروج من التطبيق؟',
              baseFontSize: ResponsiveHelper.getResponsiveFontSize(context, 16),
              style: AppStyles.getBodyStyle(context).copyWith(
                color: AppColors.secondaryText,
                height: 1.5,
              ),
              textAlign: TextAlign.right,
            ),
            SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context)),
            ResponsiveText(
              'سيتم توجيهك إلى صفحة تسجيل الدخول.',
              baseFontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
              style: AppStyles.getBodyStyle(context).copyWith(
                color: AppColors.secondaryText.withValues(alpha: 0.7),
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.right,
            ),
          ],
        ),
        actions: [
          // Logout Button
          Obx(() => ElevatedButton.icon(
            icon: profileController.isLogoutLoading.value
                ? SizedBox(
                    width: ResponsiveHelper.getResponsiveIconSize(context) * 0.8,
                    height: ResponsiveHelper.getResponsiveIconSize(context) * 0.8,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                    ),
                  )
                : Icon(
                    Icons.logout,
                    color: AppColors.white,
                    size: ResponsiveHelper.getResponsiveIconSize(context) * 0.9,
                  ),
            label: ResponsiveText(
              profileController.isLogoutLoading.value ? 'جاري تسجيل الخروج...' : 'تسجيل الخروج',
              baseFontSize: ResponsiveHelper.getResponsiveFontSize(context, 16),
              style: TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.right,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.white,
              elevation: 2,
              shadowColor: AppColors.error.withValues(alpha: 0.3),
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveHelper.getResponsiveSpacing(context) * 1.2,
                vertical: ResponsiveHelper.getResponsiveSpacing(context) * 0.8,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveHelper.getResponsiveBorderRadius(context) * 0.6,
                ),
              ),
            ),
            onPressed: profileController.isLogoutLoading.value
                ? null
                : () {
                    Get.back(); // Close dialog first
                    profileController.logout();
                  },
          )),
          // Cancel Button
          TextButton.icon(
            icon: Icon(
              Icons.cancel_outlined,
              color: AppColors.secondaryText,
              size: ResponsiveHelper.getResponsiveIconSize(context) * 0.9,
            ),
            label: ResponsiveText(
              'إلغاء',
              baseFontSize: ResponsiveHelper.getResponsiveFontSize(context, 16),
              style: TextStyle(
                color: AppColors.secondaryText,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.right,
            ),
            style: TextButton.styleFrom(
              backgroundColor: AppColors.secondaryText.withValues(alpha: 0.1),
              side: BorderSide(
                color: AppColors.secondaryText.withValues(alpha: 0.3),
                width: 1,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveHelper.getResponsiveSpacing(context) * 1.2,
                vertical: ResponsiveHelper.getResponsiveSpacing(context) * 0.8,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveHelper.getResponsiveBorderRadius(context) * 0.6,
                ),
              ),
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          
          SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
          
          
        ],
        actionsPadding: EdgeInsets.fromLTRB(
          ResponsiveHelper.getResponsiveSpacing(context) * 1.5,
          ResponsiveHelper.getResponsiveSpacing(context) * 0.5,
          ResponsiveHelper.getResponsiveSpacing(context) * 1.5,
          ResponsiveHelper.getResponsiveSpacing(context) * 1.2,
        ),
      ),
    );
  }
}