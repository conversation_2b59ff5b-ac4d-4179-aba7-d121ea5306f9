import 'package:flutter/material.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';

class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final bool respectTextDirection;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.respectTextDirection = true,
  });

  @override
  Widget build(BuildContext context) {
    Widget responsiveWidget;

    // Determine the widget to display based on screen size
    if (ResponsiveHelper.isDesktop(context) && desktop != null) {
      responsiveWidget = desktop!;
    } else if (ResponsiveHelper.isTablet(context) && tablet != null) {
      responsiveWidget = tablet!;
    } else {
      responsiveWidget = mobile;
    }

    // Always RTL for Arabic-only UI
    return Directionality(
      textDirection: TextDirection.rtl,
      child: responsiveWidget,
    );
  }

  // Create a responsive row layout
  static Widget row({
    required List<Widget> children,
    required BuildContext context,
    MainAxisAlignment? mainAxisAlignment,
    CrossAxisAlignment? crossAxisAlignment,
    MainAxisSize? mainAxisSize,
    bool respectTextDirection = true,
  }) {
    return Row(
      mainAxisAlignment: mainAxisAlignment ?? MainAxisAlignment.start,
      crossAxisAlignment: crossAxisAlignment ?? CrossAxisAlignment.center,
      mainAxisSize: mainAxisSize ?? MainAxisSize.max,
      children: children,
    );
  }
}