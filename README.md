# 🚗 United Accredited Company Driver App

A modern **Flutter-based mobile application** built for drivers at **United Accredited Company**. This app offers a seamless and efficient experience for managing transportation-related tasks, including trip tracking, flight status, vehicle maintenance requests, and real-time GPS navigation via Google Maps. It is optimized for Arabic users with full **RTL support**, robust **Supabase backend**, and **Firebase push notifications**.

---

## 📋 Overview

### ✅ Core Features
- 🔐 **Secure Driver Authentication**
- 🚚 **Trip Management & Status Updates**
- 🛫 **Flight Tracking**
- 🛠️ **Maintenance Request Submission**
- 📍 **Real-time Location Tracking via Google Maps**

---

## 🧩 Project Structure

```
lib/
├── core/               # Controllers, middleware, services, routing, theming
├── Data/               # Data models and repositories
├── presentation/       # UI (Pages & Widgets)
assets/                 # Images, JSON, and static resources
platform folders/       # android/, ios/, web/, macos/, windows/, linux/
```

---

## 💻 Language & Runtime
- **Language**: Dart
- **Framework**: Flutter
- **SDK Version**: ^3.7.2
- **Package Manager**: pub

---

## 📦 Dependencies

### ➤ Main Packages
| Package | Description |
|--------|-------------|
| `get` | State management & navigation |
| `supabase_flutter` | Backend & authentication |
| `google_maps_flutter` | Maps integration |
| `firebase_core` | Firebase setup |
| `firebase_messaging` | Push notifications |
| `flutter_local_notifications` | Local notifications |
| `connectivity_plus` | Network status |
| `location` | GPS tracking |
| `shared_preferences` | Local key-value storage |

### ➤ Dev Dependencies
- `flutter_test` → Unit & widget testing  
- `flutter_lints` → Linting & code style  
- `flutter_launcher_icons` → App icon generation

---

## ⚙️ Build & Run

```bash
# Install dependencies
flutter pub get

# Run in development mode
flutter run

# Build for Android
flutter build apk

# Build for iOS
flutter build ios

# Build for Web
flutter build web
```

---

## 🔥 Firebase Setup

- **Configuration Files**:  
  - `firebase.json`  
  - `lib/firebase_options.dart`

- **Services Used**:
  - Firebase Cloud Messaging (FCM)
  - Firebase Core for initialization

---

## 📁 Key Code Files

| Feature | Path |
|--------|------|
| Entry Point | `lib/main.dart` |
| Routing | `lib/core/routes/app_routes.dart` |
| Data Models | `lib/Data/models/` |
| Controllers | `lib/core/controller/` |
| Services | `lib/core/services/` |
| UI Pages | `lib/presentation/Pages/` |

---

## 🧪 Testing

- **Framework**: `flutter_test`
- **Test File**: `test/widget_test.dart`

```bash
flutter test
```

---

## 🧱 Clean Architecture Breakdown

This app adopts **Clean Architecture** principles with **GetX** for state management and navigation:

- 🖼️ **Presentation Layer**: Screens, pages, widgets → `presentation/`
- 🧠 **Domain Layer**: Controllers, business logic → `core/controller/`
- 💾 **Data Layer**: Models, repositories → `Data/`

---

## 🌐 Integrated Services

- **🟢 Supabase** → Realtime backend, database, and auth
- **🔥 Firebase** → Push notifications & app lifecycle handling
- **🗺️ Google Maps** → Interactive real-time location tracking

---

## 🛡️ Security & User Experience Highlights

- ✅ **Session Management** for secure access
- ✅ **Push Notifications** for real-time task updates
- ✅ **Offline Handling** with connectivity checks
- ✅ **Arabic RTL Support** with optimized layouts and fonts
- ✅ **Smooth Navigation** and animated page transitions

---

## 📌 Meta Information

- **Last Updated**: July 2025  
- **Current Version**: `1.0.0+1`  
- **Flutter SDK**: `^3.7.2`
