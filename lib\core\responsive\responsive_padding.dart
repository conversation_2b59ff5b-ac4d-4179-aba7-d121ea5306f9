import 'package:flutter/material.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';

class ResponsivePadding extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? mobilePadding;
  final EdgeInsetsGeometry? tabletPadding;
  final EdgeInsetsGeometry? desktopPadding;

  const ResponsivePadding({
    super.key,
    required this.child,
    this.mobilePadding,
    this.tabletPadding,
    this.desktopPadding,
  });

  @override
  Widget build(BuildContext context) {
    EdgeInsetsGeometry padding;

    // Determine padding based on device type
    if (ResponsiveHelper.isDesktop(context) && desktopPadding != null) {
      padding = desktopPadding!;
    } else if (ResponsiveHelper.isTablet(context) && tabletPadding != null) {
      padding = tabletPadding!;
    } else if (mobilePadding != null) {
      padding = mobilePadding!;
    } else {
      // Default padding value
      padding = EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context));
    }

    return Padding(
      padding: padding,
      child: child,
    );
  }

  // Create responsive padding with equal values on all sides
  static ResponsivePadding all(
    BuildContext context, {
    required Widget child,
    double? mobileValue,
    double? tabletValue,
    double? desktopValue,
  }) {
    final double mobileSize = mobileValue ?? ResponsiveHelper.getResponsivePadding(context);
    final double tabletSize = tabletValue ?? (mobileSize * 1.5);
    final double desktopSize = desktopValue ?? (mobileSize * 2);

    return ResponsivePadding(
      mobilePadding: EdgeInsets.all(mobileSize),
      tabletPadding: EdgeInsets.all(tabletSize),
      desktopPadding: EdgeInsets.all(desktopSize),
      child: child,
    );
  }
}
