// lib/core/middleware/auth_middleware.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/routes/app_routes.dart';
import 'package:driver_app/core/services/auth_service.dart';

class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final authService = Get.find<AuthService>();
    if (authService.currentUser.value == null && route != AppRoutes.login) {
      return RouteSettings(name: AppRoutes.login);
    }
    return null;
  }
}
