import 'package:flutter/material.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';

/// * [showModalBottomSheet]
/// * [RoundedRectangleBorder]
void showTaskDetailsBottomSheetOfDashboard(BuildContext context) {

  showModalBottomSheet(
    context: context,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(
          ResponsiveHelper.getResponsiveBorderRadius(context),
        ),
      ),
    ),
    builder: (context) {
      return Directionality(
        textDirection: TextDirection.rtl,
        child: Container(
          padding: EdgeInsets.all(
            ResponsiveHelper.getResponsivePadding(context),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(
                  'تفاصيل المهمة',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      18,
                    ),
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ),
              const Divider(),
              _buildDetailRow(
                context,
                'اسم المهمة :',
                'توصيل البضائع',
                TextAlign.right,
              ),
              _buildDetailRow(
                context,
                'الموقع :',
                'تفاصيل الموقع',
                TextAlign.right,
              ),
              _buildDetailRow(
                context,
                'تاريخ التنفيذ :',
                '12 يناير 2025',
                TextAlign.right,
              ),
              _buildDetailRow(
                context,
                'وقت التنفيذ :',
                '5:00 م',
                TextAlign.right,
              ),
              _buildDetailRow(
                context,
                'آخر موعد للتنفيذ :',
                '5:15 م',
                TextAlign.right,
              ),
              _buildDetailRow(
                context,
                'موعد التسليم النهائي :',
                '13 يناير 2025',
                TextAlign.right,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 5),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('مزيد من التفاصيل:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 5),
                    Text('لوريم إيبسوم', textAlign: TextAlign.right),
                  ],
                ),
              ),

              SizedBox(
                height: ResponsiveHelper.getResponsiveValue(
                  context,
                  mobile: 10,
                  tablet: 15,
                  desktop: 20,
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}

/// A helper widget to build a row of a form in the TaskDetailsBottomSheetOfDashboard
/// widget.
///
/// This widget takes a title and a value as parameters.
///
/// The title is displayed as a bold text and the value is displayed as a normal text.
/// The title and the value are placed side by side with a SizedBox of height 4 in
/// between.
///
/// The widget is wrapped in a Padding widget with a vertical padding of 4 for mobile,
/// 6 for tablet, and 8 for desktop.
///
/// The [textDirection] is used to determine the direction of the text in the row.
Widget _buildDetailRow(
  BuildContext context,
  String title,
  String value,
  TextAlign textAlign,
) {
  return Padding(
    padding: EdgeInsets.symmetric(
      vertical: ResponsiveHelper.getResponsiveValue(
        context,
        mobile: 4,
        tablet: 6,
        desktop: 8,
      ),
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
          ),
          textAlign: textAlign,
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
          ),
          textAlign: textAlign,
        ),
      ],
    ),
  );
}
