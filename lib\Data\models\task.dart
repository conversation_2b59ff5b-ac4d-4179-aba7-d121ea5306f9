  // import 'package:google_maps_flutter/google_maps_flutter.dart';
  import 'location_model.dart';

  class TaskModel {
    final String id;
    final String title;
    final String? description;
    final String status;
    final DateTime? dueDate;
    final LocationModel pickupLocation;
    final LocationModel dropoffLocation;

    TaskModel({
      required this.id,
      required this.title,
      this.description,
      required this.status,
      this.dueDate,
      required this.pickupLocation,
      required this.dropoffLocation,
    });

    factory TaskModel.fromJson(Map<String, dynamic> json) {
      return TaskModel(
        id: json['id'].toString(),
        title: json['title'] ?? 'No Title',
        description: json['description'],
        status: json['status'] ?? 'pending',
        dueDate:
            json['due_date'] != null ? DateTime.parse(json['due_date']) : null,
        pickupLocation: LocationModel.fromJson(json['pickup_location']),
        dropoffLocation: LocationModel.fromJson(json['dropoff_location']),
      );
    }
  }
