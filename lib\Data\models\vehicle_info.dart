class VehicleInfo {
  final String? name;
  final String? plateNumber;
  final String? number;
  final String? load;
  final String? maintenanceType;
  final String? maintenanceDetails;

  VehicleInfo({
    this.name,
    this.plateNumber,
    this.number,
    this.load,
    this.maintenanceType,
    this.maintenanceDetails,
  });

  static List<String> vehicleNames = [
    'Toyota Camry',
    'Honda Civic',
    'Ford Mustang',
    'Chevrolet Camaro',
    'Nissan Altima',
    'BMW 3 Series',
    'Audi A4',
    'Mercedes-Benz C-Class',
  ];

  static List<String> plateNumbers = [
    '1234-ABC',
    '5678-XYZ',
    '9012-DEF',
    '3456-GHI',
    '7890-JKL',
    '2345-MNO',
    '6789-PQR',
    '0123-STU',
    '4567-VWX',
    '8901-YZA',
  ];

  static List<String> vehicleNumbers = [
    '1023222',
    '1023223',
    '1023224',
    '1023225',
    '1023226',
    '1023227',
    '1023228',
    '1023229',
    '1023230',
    '1023231',
  ];

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'plateNumber': plateNumber,
      'number': number,
      'load': load,
      'maintenanceType': maintenanceType,
      'maintenanceDetails': maintenanceDetails,
    };
  }

  factory VehicleInfo.fromJson(Map<String, dynamic> json) {
    return VehicleInfo(
      name: json['name'],
      plateNumber: json['plateNumber'],
      number: json['number'],
      load: json['load'],
      maintenanceType: json['maintenanceType'],
      maintenanceDetails: json['maintenanceDetails'],
    );
  }
}