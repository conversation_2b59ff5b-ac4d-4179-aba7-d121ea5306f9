import 'package:flutter/material.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/responsive/responsive_padding.dart';
import 'package:driver_app/presentation/widgets/common/circular_app_icon.dart';

/// صفحة لعرض جميع أنواع الأيقونات المختلفة
class IconShowcaseScreen extends StatelessWidget {
  const IconShowcaseScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AppColors.primary,
          elevation: 0,
          title: const Text(
            'عرض الأيقونات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              fontFamily: 'Cairo',
            ),
          ),
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.arrow_forward, color: Colors.white),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: SingleChildScrollView(
          child: ResponsivePadding.all(
            context,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionTitle('أيقونات بأحجام مختلفة'),
                _buildIconSizesSection(),
                const SizedBox(height: 30),
                
                _buildSectionTitle('أيقونات مع حدود مختلفة'),
                _buildIconBordersSection(),
                const SizedBox(height: 30),
                
                _buildSectionTitle('أيقونات مع تسميات'),
                _buildIconLabelsSection(),
                const SizedBox(height: 30),
                
                _buildSectionTitle('أيقونات قابلة للضغط'),
                _buildClickableIconsSection(),
                const SizedBox(height: 30),
                
                _buildSectionTitle('أيقونات مع تأثيرات مختلفة'),
                _buildIconEffectsSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }

  Widget _buildIconSizesSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          CircularAppIcon(radius: 20),
          CircularAppIcon(radius: 30),
          CircularAppIcon(radius: 40),
          CircularAppIcon(radius: 50),
        ],
      ),
    );
  }

  Widget _buildIconBordersSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          CircularAppIcon(
            radius: 35,
            showBorder: false,
          ),
          CircularAppIcon(
            radius: 35,
            borderColor: AppColors.primary,
            borderWidth: 2,
          ),
          CircularAppIcon(
            radius: 35,
            borderColor: AppColors.success,
            borderWidth: 3,
          ),
          CircularAppIcon(
            radius: 35,
            borderColor: AppColors.error,
            borderWidth: 4,
          ),
        ],
      ),
    );
  }

  Widget _buildIconLabelsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          CircularAppIconWithLabel(
            radius: 30,
            label: 'سائق',
          ),
          CircularAppIconWithLabel(
            radius: 30,
            label: 'تطبيق',
            borderColor: AppColors.success,
          ),
          CircularAppIconWithLabel(
            radius: 30,
            label: 'خدمة',
            borderColor: AppColors.warning,
          ),
        ],
      ),
    );
  }

  Widget _buildClickableIconsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          CircularAppIconButton(
            radius: 35,
            onTap: () => _showSnackBar('تم الضغط على الأيقونة الأولى'),
            showRipple: true,
          ),
          CircularAppIconButton(
            radius: 35,
            borderColor: AppColors.success,
            onTap: () => _showSnackBar('تم الضغط على الأيقونة الثانية'),
            showRipple: false,
          ),
          CircularAppIconButton(
            radius: 35,
            borderColor: AppColors.warning,
            onTap: () => _showSnackBar('تم الضغط على الأيقونة الثالثة'),
          ),
        ],
      ),
    );
  }

  Widget _buildIconEffectsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          CircularAppIcon(
            radius: 35,
            showShadow: false,
          ),
          CircularAppIcon(
            radius: 35,
            showShadow: true,
          ),
          CircularAppIcon(
            radius: 35,
            showShadow: true,
            borderColor: AppColors.primary,
            borderWidth: 3,
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message) {
    // يمكن إضافة SnackBar هنا لعرض الرسالة
    print(message);
  }
}