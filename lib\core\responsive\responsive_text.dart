import 'package:flutter/material.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';

class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final double baseFontSize;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextDirection? textDirection;
  final bool? softWrap;
  final double? textScaleFactor;
  final StrutStyle? strutStyle;
  final TextWidthBasis? textWidthBasis;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.baseFontSize = 14.0,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.textDirection,
    this.softWrap,
    this.textScaleFactor,
    this.strutStyle,
    this.textWidthBasis,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate responsive font size
    final responsiveFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      baseFontSize,
    );

    return Text(
      text,
      style: style?.copyWith(fontSize: responsiveFontSize) ??
          TextStyle(fontSize: responsiveFontSize),
      textAlign: textAlign ?? TextAlign.right,
      maxLines: maxLines,
      overflow: overflow,
      textDirection: TextDirection.rtl,
      softWrap: softWrap,
      textScaler: textScaleFactor != null ? TextScaler.linear(textScaleFactor!) : null,
      strutStyle: strutStyle,
      textWidthBasis: textWidthBasis,
    );
  }

  // Create a modified copy of the ResponsiveText widget
  ResponsiveText copyWith({
    String? text,
    TextStyle? style,
    double? baseFontSize,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    TextDirection? textDirection,
    bool? softWrap,
    double? textScaleFactor,
    StrutStyle? strutStyle,
    TextWidthBasis? textWidthBasis,
  }) {
    return ResponsiveText(
      text ?? this.text,
      style: style ?? this.style,
      baseFontSize: baseFontSize ?? this.baseFontSize,
      textAlign: textAlign ?? this.textAlign,
      maxLines: maxLines ?? this.maxLines,
      overflow: overflow ?? this.overflow,
      textDirection: textDirection ?? this.textDirection,
      softWrap: softWrap ?? this.softWrap,
      textScaleFactor: textScaleFactor ?? this.textScaleFactor,
      strutStyle: strutStyle ?? this.strutStyle,
      textWidthBasis: textWidthBasis ?? this.textWidthBasis,
    );
  }

  // Create a heading-style ResponsiveText widget
  static ResponsiveText heading(
    String text, {
    BuildContext? context,
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    Color? color,
  }) {
    return ResponsiveText(
      text,
      baseFontSize: 24,
      style: style?.copyWith(
        fontWeight: FontWeight.bold,
        color: color,
      ) ??
          TextStyle(
            fontWeight: FontWeight.bold,
            color: color ?? Colors.black,
          ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}