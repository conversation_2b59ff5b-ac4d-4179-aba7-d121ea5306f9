name: driver_app
description: "A mobile app for company drivers."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get: ^4.7.2
  intl: ^0.19.0
  supabase_flutter: ^2.8.4
  fluttertoast: ^8.2.12
  connectivity_plus: ^6.1.3
  lottie: ^3.3.1
  google_maps_flutter: ^2.12.1
  location: ^8.0.0
  flutter_polyline_points: ^2.1.0
  shared_preferences: ^2.5.3
  firebase_core: ^3.13.1
  firebase_messaging: ^15.2.6
  flutter_local_notifications: ^19.2.1
  googleapis_auth: ^2.0.0
  app_links: ^6.4.0
  image_picker: ^1.1.2
  permission_handler: ^11.3.1


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/json/uac-noto-b29e74ac0958.json

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/driver.jpeg"
  min_sdk_android: 21 # Android min SDK
  
  # تكوين الأيقونة الدائرية للأندرويد
  adaptive_icon_background: "#2196F3" # لون الخلفية الأزرق
  adaptive_icon_foreground: "assets/images/driver.jpeg" # الصورة الأمامية
  
  # إعدادات الويب
  web:
    generate: true
    image_path: "assets/images/driver.jpeg"
    background_color: "#2196F3"
    theme_color: "#2196F3"
  
  # إعدادات ويندوز
  windows:
    generate: true
    image_path: "assets/images/driver.jpeg"
    icon_size: 48
  
  # إعدادات ماك
  macos:
    generate: true
    image_path: "assets/images/driver.jpeg"
