# United Accredited Company Driver App Information

## Summary
A Flutter-based mobile application for drivers of United Accredited Company. The app provides functionality for driver authentication, trip management, flight tracking, vehicle maintenance requests, and real-time location tracking using Google Maps.

## Structure
- **lib/**: Core application code
  - **core/**: Core functionality (controllers, middleware, services, routes, themes)
  - **Data/**: Data models and repositories
  - **presentation/**: UI components (pages, widgets)
- **assets/**: Application resources (images, JSON files)
- **android/**, **ios/**, **web/**, **macos/**, **linux/**, **windows/**: Platform-specific code

## Language & Runtime
**Language**: Dart
**Version**: SDK ^3.7.2
**Framework**: Flutter
**Package Manager**: pub (Flutter/Dart package manager)

## Dependencies
**Main Dependencies**:
- flutter: SDK
- get: ^4.7.2 (State management)
- supabase_flutter: ^2.8.4 (Backend as a Service)
- google_maps_flutter: ^2.12.1 (Maps integration)
- firebase_core: ^3.13.1 (Firebase integration)
- firebase_messaging: ^15.2.6 (Push notifications)
- flutter_local_notifications: ^19.2.1 (Local notifications)
- connectivity_plus: ^6.1.3 (Network connectivity)
- location: ^8.0.0 (Location services)
- shared_preferences: ^2.5.3 (Local storage)

**Development Dependencies**:
- flutter_test: SDK
- flutter_lints: ^5.0.0 (Code quality)
- flutter_launcher_icons: ^0.14.1 (App icon generation)

## Build & Installation
```bash
# Install dependencies
flutter pub get

# Run the application in development mode
flutter run

# Build for Android
flutter build apk

# Build for iOS
flutter build ios

# Build for web
flutter build web
```

## Firebase Integration
**Configuration**: firebase.json and lib/firebase_options.dart
**Services**: 
- Firebase Cloud Messaging for push notifications
- Firebase Core for app initialization

## Main Files
**Entry Point**: lib/main.dart
**Routes**: lib/core/routes/app_routes.dart
**Models**: lib/Data/models/
**Controllers**: lib/core/controller/
**Services**: lib/core/services/
**UI Pages**: lib/presentation/Pages/

## Testing
**Framework**: flutter_test
**Test Location**: test/
**Test Files**: widget_test.dart
**Run Command**:
```bash
flutter test
```

## Architecture
The application follows a clean architecture approach with GetX for state management:
- **Presentation Layer**: UI components in presentation/
- **Domain Layer**: Business logic in core/controller/
- **Data Layer**: Data models and repositories in Data/

The app uses Supabase as a backend service and integrates with Firebase for notifications. It implements Google Maps for location tracking and navigation features.