import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/controller/profile_controller.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';
import 'package:driver_app/core/responsive/responsive_text.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/theme/app_styles.dart';
import 'package:driver_app/presentation/widgets/dialogs/logout_confirmation_dialog.dart';
import 'package:driver_app/presentation/widgets/bottom_sheets/image_picker_bottom_sheet.dart';
import 'package:driver_app/presentation/widgets/profile/profile_avatar_widget.dart';

/// Demo widget to test both Profile features:
/// 1. Logout Flow with Confirmation Dialog
/// 2. Profile Image Capture/Selection
class ProfileFeaturesDemo extends StatelessWidget {
  const ProfileFeaturesDemo({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize ProfileController
    final ProfileController profileController = Get.put(ProfileController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile Features Demo'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Padding(
          padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Feature 1: Profile Image Selection Demo
              _buildFeatureSection(
                context,
                title: '🖼️ اختيار صورة الملف الشخصي',
                description: 'اضغط على الصورة لتجربة اختيار صورة من الكاميرا أو المعرض',
                child: Center(
                  child: ProfileAvatarWidget(
                    radius: ResponsiveHelper.getValue(context, 60.0, 70.0, 80.0),
                    showEditButton: true,
                  ),
                ),
              ),
              
              SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 2),
              
              // Image Status Display
              Obx(() => Container(
                padding: EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(context)),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(
                    ResponsiveHelper.getResponsiveBorderRadius(context),
                  ),
                  border: Border.all(
                    color: AppColors.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      profileController.selectedProfileImage.value != null
                          ? Icons.check_circle
                          : Icons.info,
                      color: profileController.selectedProfileImage.value != null
                          ? AppColors.success
                          : AppColors.primary,
                    ),
                    SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
                    Expanded(
                      child: ResponsiveText(
                        profileController.selectedProfileImage.value != null
                            ? 'تم اختيار الصورة: ${profileController.selectedProfileImage.value!.name}'
                            : 'لم يتم اختيار صورة بعد',
                        baseFontSize: 14,
                        style: AppStyles.getBodyStyle(context),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              )),
              
              SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 3),
              
              // Feature 2: Logout Flow Demo
              _buildFeatureSection(
                context,
                title: '🔐 تدفق تسجيل الخروج',
                description: 'اضغط على الزر لتجربة حوار تأكيد تسجيل الخروج',
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.logout),
                    label: ResponsiveText(
                      'تسجيل الخروج',
                      baseFontSize: 16,
                      style: const TextStyle(
                        color: AppColors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error,
                      foregroundColor: AppColors.white,
                      padding: EdgeInsets.symmetric(
                        vertical: ResponsiveHelper.getResponsiveSpacing(context) * 1.2,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          ResponsiveHelper.getResponsiveBorderRadius(context),
                        ),
                      ),
                    ),
                    onPressed: () => _showLogoutDialog(),
                  ),
                ),
              ),
              
              SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 2),
              
              // Additional Test Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      icon: const Icon(Icons.photo_library),
                      label: const Text('معرض الصور'),
                      onPressed: () => _showImagePicker(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.primary,
                        side: const BorderSide(color: AppColors.primary),
                      ),
                    ),
                  ),
                  SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context)),
                  Expanded(
                    child: OutlinedButton.icon(
                      icon: const Icon(Icons.clear),
                      label: const Text('مسح الصورة'),
                      onPressed: () => profileController.clearSelectedImage(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.secondaryText,
                        side: const BorderSide(color: AppColors.secondaryText),
                      ),
                    ),
                  ),
                ],
              ),
              
              const Spacer(),
              
              // Status Information
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(context)),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(
                    ResponsiveHelper.getResponsiveBorderRadius(context),
                  ),
                  border: Border.all(
                    color: AppColors.success.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      '✅ الميزات المُنفذة بنجاح:',
                      baseFontSize: 16,
                      style: AppStyles.getHeadingStyle(context).copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.right,
                    ),
                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
                    ResponsiveText(
                      '• حوار تأكيد تسجيل الخروج مع إلغاء صحيح\n'
                      '• اختيار الصورة من الكاميرا والمعرض\n'
                      '• عرض الصورة المختارة في الأفاتار\n'
                      '• حفظ مسار الصورة في المتحكم\n'
                      '• تصميم متجاوب وحديث',
                      baseFontSize: 14,
                      style: AppStyles.getBodyStyle(context).copyWith(
                        color: AppColors.success,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureSection(
    BuildContext context, {
    required String title,
    required String description,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(context) * 1.5),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            title,
            baseFontSize: 18,
            style: AppStyles.getHeadingStyle(context).copyWith(
              color: AppColors.primaryText,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.right,
          ),
          SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
          ResponsiveText(
            description,
            baseFontSize: 14,
            style: AppStyles.getBodyStyle(context).copyWith(
              color: AppColors.secondaryText,
              height: 1.4,
            ),
            textAlign: TextAlign.right,
          ),
          SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 1.2),
          child,
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    Get.dialog(const LogoutConfirmationDialog());
  }

  void _showImagePicker() {
    Get.bottomSheet(
      const ImagePickerBottomSheet(),
      isScrollControlled: true,
    );
  }
}