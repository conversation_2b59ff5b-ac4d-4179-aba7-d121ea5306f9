import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:driver_app/main.dart';
import 'package:fluttertoast/fluttertoast.dart';

class NoInternetConnectionApp extends StatelessWidget {
  const NoInternetConnectionApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.wifi_off, size: 64, color: Colors.red),
              SizedBox(height: 20),
              Text('لا يوجد اتصال بالإنترنت', style: TextStyle(fontSize: 24)),
              SizedBox(height: 20),
              ElevatedButton(
                onPressed: () async {
                  final connectivityResult =
                      await Connectivity().checkConnectivity();
                  if (connectivityResult != ConnectivityResult.none) {
                    main(); // إعادة تشغيل التطبيق
                  } else {
                    Fluttertoast.showToast(
                      msg: "لا يزال الاتصال غير متاح",
                      toastLength: Toast.LENGTH_SHORT,
                      gravity: ToastGravity.BOTTOM,
                    );
                  }
                },
                child: Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
