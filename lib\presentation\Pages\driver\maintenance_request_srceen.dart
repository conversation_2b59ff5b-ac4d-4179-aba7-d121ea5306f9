import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:driver_app/core/controller/user_controller.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';
import 'package:driver_app/core/services/firebase/send_notification.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_styles.dart';
import '../../widgets/navigation/navigation_button_bar.dart';

class AssignedVehiclePage extends StatefulWidget {
  const AssignedVehiclePage({super.key});

  @override
  State<AssignedVehiclePage> createState() => _AssignedVehiclePageState();
}

class _AssignedVehiclePageState extends State<AssignedVehiclePage> {
  final _userController = Get.find<UserController>();
  final SupabaseClient _supabase = Supabase.instance.client;
  Map<String, dynamic>? _vehicleAssignment;
  bool _isLoading = true;
  bool _showMaintenanceForm = false;

  final TextEditingController _descController = TextEditingController();
  final TextEditingController _costController = TextEditingController();
  bool _isEmergency = false;

  @override
  void initState() {
    super.initState();
    _fetchVehicleData();
  }

  Future<void> _fetchVehicleData() async {
    try {
      final driverId = _userController.currentUser?.driverDetails?.id;
      if (driverId == null) return;

      final response =
          await _supabase
              .from('vehicle_assignments')
              .select('''
            id, start_date, end_date, is_active,
            vehicles (id, plate_number, model, status)
          ''')
              .eq('driver_id', driverId)
              .eq('is_active', true)
              .single();

      setState(() {
        _vehicleAssignment = response;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      Get.snackbar('خطأ', 'فشل في تحميل بيانات المركبة');
    }
  }

  Future<void> _logActivity(String action, String description) async {
    try {
      final supabase = Supabase.instance.client;
      final userController = Get.find<UserController>();
      final user = userController.user.value;
      if (user == null || user.driverDetails == null) return;

      // الحصول على user_id من جدول السائقين
      final driverResponse =
          await supabase
              .from('drivers')
              .select('user_id')
              .eq('id', user.driverDetails!.id)
              .single();

      final userId = driverResponse['user_id'];
      final fullDescription = '${user.fullName} - $description';

      await supabase.from('activity_logs').insert({
        'user_id': userId,
        'action': action,
        'description': fullDescription,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('فشل في تسجيل النشاط: $e');
    }
  }

  Future<void> _submitMaintenance() async {
    if (_descController.text.isEmpty) {
      Get.snackbar('خطأ', 'يرجى إدخال وصف الصيانة');
      return;
    }

    try {
      await _supabase.from('maintenance').insert({
        'vehicle_assets': _vehicleAssignment?['id'],
        'description': _descController.text,
        'cost':
            _costController.text.isNotEmpty
                ? double.parse(_costController.text)
                : null,
        'emergency': _isEmergency,
        'done_or_not': false,
        'created_at': DateTime.now().toIso8601String(),
      });

      _logActivity('طلب صيانة', 'تم إرسال طلب صيانة');
      sendtoNotification('طلب صيانة','${_userController.currentUser?.fullName} أرسل طلب صيانة');

      Get.snackbar(
        'نجاح',
        'تم إرسال طلب الصيانة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      _resetForm();
    } catch (e) {
      print(e.toString());
      Get.snackbar(
        'خطأ',
        'فشل في إرسال طلب الصيانة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _resetForm() {
    setState(() {
      _showMaintenanceForm = false;
      _descController.clear();
      _costController.clear();
      _isEmergency = false;
    });
  }

  Widget _buildVehicleCard() {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          strokeWidth: 3,
        ),
      );
    }

    if (_vehicleAssignment == null) {
      return Directionality(
        textDirection: TextDirection.rtl,
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context) * 2),
          decoration: BoxDecoration(
            color: AppColors.lightGrey.withOpacity(0.3),
            borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
            border: Border.all(color: AppColors.dividerColor.withOpacity(0.3)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.directions_car_outlined,
                size: ResponsiveHelper.getResponsiveIconSize(context) * 2,
                color: AppColors.secondaryText,
              ),
              SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context)),
              Text(
                'لم يتم العثور على مركبة مخصصة',
                style: AppStyles.getSubheadingStyle(context).copyWith(
                  color: AppColors.secondaryText,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    final vehicle = _vehicleAssignment!['vehicles'];
    final startDate = DateTime.parse(_vehicleAssignment!['start_date']);
    final endDate = DateTime.parse(_vehicleAssignment!['end_date']);

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Card(
        elevation: 2,
        shadowColor: AppColors.primary.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context) * 1.5),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.cardBackground,
            borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context) * 1.5),
            border: Border.all(
              color: AppColors.primary.withOpacity(0.6),
              width: 1,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context) * 1.5),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Section
                Container(
                  padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context)),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context) * 0.75),
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
                        ),
                        child: Icon(
                          Icons.directions_car,
                          color: AppColors.white,
                          size: ResponsiveHelper.getResponsiveIconSize(context),
                        ),
                      ),
                      SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context)),
                      Expanded(
                        child: Text(
                          'تفاصيل المركبة المخصصة',
                          style: AppStyles.getSubheadingStyle(context).copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: ResponsiveHelper.getResponsivePadding(context),
                          vertical: ResponsiveHelper.getResponsivePadding(context) * 0.5,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(vehicle['status']).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context) * 2),
                          border: Border.all(
                            color: _getStatusColor(vehicle['status']).withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          _getStatusText(vehicle['status']),
                          style: AppStyles.getCaptionStyle(context).copyWith(
                            color: _getStatusColor(vehicle['status']),
                            fontWeight: FontWeight.w600,
                            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 1.5),
                
                // Vehicle Information Section
                _buildInfoRow('الموديل', vehicle['model'], Icons.car_rental_outlined),
                SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context)),
                _buildInfoRow('رقم اللوحة', vehicle['plate_number'], Icons.confirmation_number_outlined),
                
                Padding(
                  padding: EdgeInsets.symmetric(vertical: ResponsiveHelper.getResponsiveSpacing(context)),
                  child: Divider(
                    color: AppColors.dividerColor.withOpacity(0.3),
                    thickness: 1,
                  ),
                ),
                
                // Assignment Period Section
                Container(
                  padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context)),
                  decoration: BoxDecoration(
                    color: AppColors.lightGrey.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.schedule_outlined,
                            color: AppColors.primary,
                            size: ResponsiveHelper.getResponsiveIconSize(context) * 0.9,
                          ),
                          SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
                          Text(
                            'فترة التخصيص',
                            style: AppStyles.getBodyStyle(context).copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context)),
                      _buildDateRow('تاريخ البدء', startDate),
                      SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
                      _buildDateRow('تاريخ الانتهاء', endDate),
                    ],
                  ),
                ),
                
                SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 1.5),
                _buildMaintenanceButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String title, String value, IconData icon) {
    return Container(
      padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
        border: Border.all(
          color: AppColors.borderColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context) * 0.5),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context) * 0.5),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: ResponsiveHelper.getResponsiveIconSize(context) * 0.8,
            ),
          ),
          SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppStyles.getCaptionStyle(context).copyWith(
                    color: AppColors.secondaryText,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  value,
                  style: AppStyles.getBodyStyle(context).copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'متاح':
        return AppColors.success;
      case 'maintenance':
      case 'صيانة':
        return AppColors.warning;
      case 'inactive':
      case 'غير متاح':
        return AppColors.statusInactive;
      default:
        return AppColors.primary;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return 'متاح';
      case 'maintenance':
        return 'صيانة';
      case 'inactive':
        return 'غير متاح';
      default:
        return status;
    }
  }

  Widget _buildDateRow(String title, DateTime date) {
    return Row(
      children: [
        Icon(
          Icons.calendar_today_outlined,
          color: AppColors.secondaryText,
          size: ResponsiveHelper.getResponsiveIconSize(context) * 0.7,
        ),
        SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context) * 0.75),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: AppStyles.getCaptionStyle(context).copyWith(
                  color: AppColors.secondaryText,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: ResponsiveHelper.getResponsivePadding(context) * 0.75,
                  vertical: ResponsiveHelper.getResponsivePadding(context) * 0.25,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context) * 0.5),
                  border: Border.all(
                    color: AppColors.primary.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}',
                  style: AppStyles.getCaptionStyle(context).copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMaintenanceButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.secondary],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
          onTap: () => setState(() => _showMaintenanceForm = true),
          child: Container(
            padding: EdgeInsets.symmetric(
              vertical: ResponsiveHelper.getResponsivePadding(context),
              horizontal: ResponsiveHelper.getResponsivePadding(context) * 1.5,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context) * 0.5),
                  decoration: BoxDecoration(
                    color: AppColors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context) * 0.5),
                  ),
                  child: Icon(
                    Icons.build_circle_outlined,
                    color: AppColors.white,
                    size: ResponsiveHelper.getResponsiveIconSize(context),
                  ),
                ),
                SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context)),
                Text(
                  'طلب صيانة جديد',
                  style: AppStyles.getBodyStyle(context).copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.white.withOpacity(0.8),
                  size: ResponsiveHelper.getResponsiveIconSize(context) * 0.7,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMaintenanceForm() {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        margin: EdgeInsets.only(top: ResponsiveHelper.getResponsiveSpacing(context) * 1.5),
        decoration: BoxDecoration(
          color: AppColors.cardBackground,
          borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context) * 1.5),
          border: Border.all(
            color: AppColors.primary.withOpacity(0.1),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withOpacity(0.08),
              blurRadius: 20,
              spreadRadius: 0,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context) * 1.5),
          child: Column(
            children: [
              // Header Section
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context) * 1.5),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary.withOpacity(0.1),
                      AppColors.secondary.withOpacity(0.05),
                    ],
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                  ),
                  border: Border(
                    bottom: BorderSide(
                      color: AppColors.primary.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context) * 0.75),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
                      ),
                      child: Icon(
                        Icons.build_circle_outlined,
                        color: AppColors.white,
                        size: ResponsiveHelper.getResponsiveIconSize(context),
                      ),
                    ),
                    SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context)),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'طلب صيانة جديد',
                            style: AppStyles.getSubheadingStyle(context).copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          Text(
                            'املأ البيانات المطلوبة أدناه',
                            style: AppStyles.getCaptionStyle(context).copyWith(
                              color: AppColors.secondaryText,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: _resetForm,
                      icon: Icon(
                        Icons.close_rounded,
                        color: AppColors.secondaryText,
                        size: ResponsiveHelper.getResponsiveIconSize(context),
                      ),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),

              // Form Content
              Padding(
                padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context) * 1.5),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Description Field
                    _buildFormField(
                      label: 'وصف المشكلة',
                      icon: Icons.description_outlined,
                      isRequired: true,
                      child: TextField(
                        controller: _descController,
                        decoration: AppStyles.getInputDecoration(
                          context,
                          labelText: 'اكتب وصفاً مفصلاً للمشكلة...',
                          prefixIcon: Icon(
                            Icons.description_outlined,
                            color: AppColors.primary,
                            size: ResponsiveHelper.getResponsiveIconSize(context) * 0.9,
                          ),
                        ).copyWith(
                          filled: true,
                          fillColor: AppColors.lightGrey.withOpacity(0.3),
                          contentPadding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context)),
                        ),
                        maxLines: 4,
                        minLines: 3,
                        style: AppStyles.getBodyStyle(context),
                        textDirection: TextDirection.rtl,
                      ),
                    ),

                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 1.5),

                    // Cost Field
                    _buildFormField(
                      label: 'التكلفة التقديرية',
                      icon: Icons.attach_money_outlined,
                      isRequired: false,
                      child: TextField(
                        controller: _costController,
                        keyboardType: TextInputType.number,
                        decoration: AppStyles.getInputDecoration(
                          context,
                          labelText: 'أدخل التكلفة التقديرية (اختياري)',
                          prefixIcon: Icon(
                            Icons.attach_money_outlined,
                            color: AppColors.primary,
                            size: ResponsiveHelper.getResponsiveIconSize(context) * 0.9,
                          ),
                          suffixIcon: Container(
                            margin: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context) * 0.5),
                            padding: EdgeInsets.symmetric(
                              horizontal: ResponsiveHelper.getResponsivePadding(context) * 0.75,
                              vertical: ResponsiveHelper.getResponsivePadding(context) * 0.5,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context) * 0.5),
                            ),
                            child: Text(
                              'ريال',
                              style: AppStyles.getCaptionStyle(context).copyWith(
                                color: AppColors.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ).copyWith(
                          filled: true,
                          fillColor: AppColors.lightGrey.withOpacity(0.3),
                        ),
                        style: AppStyles.getBodyStyle(context),
                        textDirection: TextDirection.rtl,
                      ),
                    ),

                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 1.5),

                    // Emergency Switch
                    Container(
                      padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context)),
                      decoration: BoxDecoration(
                        color: _isEmergency 
                          ? AppColors.error.withOpacity(0.05)
                          : AppColors.lightGrey.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
                        border: Border.all(
                          color: _isEmergency 
                            ? AppColors.error.withOpacity(0.3)
                            : AppColors.borderColor.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context) * 0.5),
                            decoration: BoxDecoration(
                              color: _isEmergency 
                                ? AppColors.error.withOpacity(0.1)
                                : AppColors.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context) * 0.5),
                            ),
                            child: Icon(
                              _isEmergency ? Icons.priority_high : Icons.info_outline,
                              color: _isEmergency ? AppColors.error : AppColors.primary,
                              size: ResponsiveHelper.getResponsiveIconSize(context) * 0.8,
                            ),
                          ),
                          SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context)),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'صيانة طارئة',
                                  style: AppStyles.getBodyStyle(context).copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: _isEmergency ? AppColors.error : AppColors.primaryText,
                                  ),
                                ),
                                if (_isEmergency)
                                  Text(
                                    'سيتم التعامل مع الطلب بأولوية عالية',
                                    style: AppStyles.getCaptionStyle(context).copyWith(
                                      color: AppColors.error,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          Switch.adaptive(
                            value: _isEmergency,
                            activeColor: AppColors.error,
                            inactiveThumbColor: AppColors.white,
                            inactiveTrackColor: AppColors.lightGrey,
                            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            onChanged: (value) => setState(() => _isEmergency = value),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 2),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _resetForm,
                            style: OutlinedButton.styleFrom(
                              foregroundColor: AppColors.secondaryText,
                              side: BorderSide(
                                color: AppColors.borderColor.withOpacity(0.5),
                                width: 1,
                              ),
                              padding: EdgeInsets.symmetric(
                                vertical: ResponsiveHelper.getResponsivePadding(context),
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.close_rounded,
                                  size: ResponsiveHelper.getResponsiveIconSize(context) * 0.8,
                                ),
                                SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
                                Text(
                                  'إلغاء',
                                  style: AppStyles.getBodyStyle(context).copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context)),
                        Expanded(
                          flex: 2,
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [AppColors.primary, AppColors.secondary],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.primary.withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(ResponsiveHelper.getResponsiveBorderRadius(context)),
                                onTap: _submitMaintenance,
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                    vertical: ResponsiveHelper.getResponsivePadding(context),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.send_rounded,
                                        color: AppColors.white,
                                        size: ResponsiveHelper.getResponsiveIconSize(context) * 0.8,
                                      ),
                                      SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
                                      Text(
                                        'إرسال الطلب',
                                        style: AppStyles.getBodyStyle(context).copyWith(
                                          color: AppColors.white,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required IconData icon,
    required bool isRequired,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: AppColors.primary,
              size: ResponsiveHelper.getResponsiveIconSize(context) * 0.8,
            ),
            SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
            Text(
              label,
              style: AppStyles.getBodyStyle(context).copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.primaryText,
              ),
            ),
            if (isRequired) ...[
              SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context) * 0.25),
              Text(
                '*',
                style: AppStyles.getBodyStyle(context).copyWith(
                  color: AppColors.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
        SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 0.75),
        child,
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
         backgroundColor: const Color(0xFF2196F3),
      elevation: 0,
      surfaceTintColor: Colors.transparent,
        title: Text('طلب صيانة' ,
         style: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontFamily: 'Cairo',
        ),
         ),
        centerTitle: true,
        
         leading: IconButton(
          icon: const Icon(
            Icons.account_circle,
            color: Colors.black38,
            size: 35,
          ),
          onPressed: () {
            Get.toNamed(AppRoutes.profile);
          },
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.notifications_outlined,
              color:Colors.white,
              size: ResponsiveHelper.getResponsiveIconSize(Get.context!),
            ),
            onPressed: () {},
          ),
        ],
      ),
      body: Container(
        color: AppColors.background,
        child: Padding(
          padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context) * 1.25),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    children: [
                      _buildVehicleCard(),
                      if (_showMaintenanceForm) _buildMaintenanceForm(),
                      SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 2),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: NavigationButtonBar(
        items: [
          NavigationItem(
            icon: Icons.home,
            label: 'لوحة التحكم',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.dashboard),
          ),
          NavigationItem(
            icon: Icons.assignment,
            label: 'سجل النشاط',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () {
              final driverId = _userController.currentUser?.driverDetails?.id;
              if (driverId != null) {
                Get.toNamed(
                  AppRoutes.activityLog,
                  arguments: {'driverId': driverId},
                );
              } else {
                Get.snackbar('خطأ', 'معرف السائق غير متوفر');
              }
            },
          ),
          NavigationItem(
            icon: Icons.build,
            label: 'صيانة',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.maintenance),
          ),
          NavigationItem(
            icon: Icons.map,
            label: 'خريطة',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.mapScreen),
          ),
        ],
        currentIndex: 2,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: Colors.grey,
        onItemSelected: (index) {
          // Handle item selection
          print('Selected index: $index');
        },
        shadowColor: Colors.black.withOpacity(0.2),
      ),
    );
  }
  Future<void> sendtoNotification(String title, String des) async {
    final supabase = Supabase.instance.client;
    try {
          print("====================================================");

    final supervisorID = await supabase
        .from('drivers')
        .select('supervisor_id')
        .eq('id', _userController.currentUser?.driverDetails!.id ?? '')
        .single();

    final supervisorIdValue = supervisorID['supervisor_id'];
    print('Supervisor ID: $supervisorIdValue');

    final supervisorTokenRes = await supabase
        .from('supervisors')
        .select('user_id')
        .eq('id', supervisorIdValue);

    final supervisorUserId = supervisorTokenRes.first['user_id'];
    print('Supervisor User ID: $supervisorUserId');

    final tokenRes = await supabase
        .from('device_tokens')
        .select('token')
        .eq('user_id', supervisorUserId);

    final targetToken = tokenRes.first['token'];
    print('Target Token: $targetToken');
    print("====================================================");
      sendFCMv1Notification(
        targetToken:
            targetToken.toString(),
        // targetToken:
        //     'fkvvI51kQdKleFArRWz4g4:APA91bFGj67j1Lz5j4KFqpSua9j_ZOrZ1uGju2T6_sRKjCT_oeAkt2-tsgAaAm6cb3RB8ej8WJ4RXvPfJh4WkTOWZ663Z6An0Sjw6iK1lJPUOyyPubKbdIU',
        title: title,
        body: des,
      );
    } catch (e) {
      print(e.toString());
    }
  }
}
