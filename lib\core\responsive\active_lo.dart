import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:driver_app/Data/models/activity_logs.dart';

class ActivityLogRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Fetch activity logs for a specific driver using JOIN
  Future<List<ActivityLogModel>> getDriverActivityLogs(String driverId) async {
    try {
      // أول شيء نحصل user_id من جدول drivers
      final driverResponse =
          await _supabase
              .from('drivers')
              .select('user_id')
              .eq('id', int.parse(driverId))
              .single();

      final userId = driverResponse['user_id'];

      // ثم نجيب النشاطات المرتبطة بهذا user_id
      final logsResponse = await _supabase
          .from('activity_logs')
          .select('id, user_id, action, description, created_at')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return logsResponse.map<ActivityLogModel>((log) {
        return ActivityLogModel(
          id: log['id']?.toString() ?? '',
          userId: log['user_id']?.toString() ?? '',
          action: log['action'] ?? '',
          description: log['description'] ?? '',
          createdAt:
              log['created_at'] != null
                  ? DateTime.parse(log['created_at'])
                  : DateTime.now(),
        );
      }).toList();
    } catch (e) {
      throw Exception('فشل في تحميل الانشطة: ${e.toString()}');
    }
  }
}
