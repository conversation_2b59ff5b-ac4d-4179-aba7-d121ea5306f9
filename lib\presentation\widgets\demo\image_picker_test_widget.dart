import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/controller/profile_controller.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';
import 'package:driver_app/core/responsive/responsive_text.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/theme/app_styles.dart';
import 'package:driver_app/presentation/widgets/profile/profile_avatar_widget.dart';

/// Enhanced test widget to verify all image picker scenarios:
/// ✅ Camera capture with permission handling
/// ✅ Gallery selection with permission handling  
/// ✅ User cancellation handling (no errors)
/// ✅ Permission denied scenarios
/// ✅ Error handling for real exceptions
class ImagePickerTestWidget extends StatelessWidget {
  const ImagePickerTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final ProfileController profileController = Get.put(ProfileController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('🧪 Image Picker Test'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(ResponsiveHelper.getResponsivePadding(context)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Test Status Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(context) * 1.5),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary.withValues(alpha: 0.1),
                      AppColors.accent.withValues(alpha: 0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(
                    ResponsiveHelper.getResponsiveBorderRadius(context),
                  ),
                  border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                ),
                child: Column(
                  children: [
                    ResponsiveText(
                      '🛠️ اختبار ميزة اختيار الصور',
                      baseFontSize: 20,
                      style: AppStyles.getHeadingStyle(context).copyWith(
                        color: AppColors.primaryText,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
                    ResponsiveText(
                      'اختبار جميع الحالات: النجاح، الإلغاء، الأخطاء، والأذونات',
                      baseFontSize: 14,
                      style: AppStyles.getBodyStyle(context).copyWith(
                        color: AppColors.secondaryText,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 2),
              
              // Profile Avatar Section
              Container(
                padding: EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(context) * 2),
                decoration: BoxDecoration(
                  color: AppColors.cardBackground,
                  borderRadius: BorderRadius.circular(
                    ResponsiveHelper.getResponsiveBorderRadius(context),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    ResponsiveText(
                      '📸 صورة الملف الشخصي',
                      baseFontSize: 18,
                      style: AppStyles.getHeadingStyle(context).copyWith(
                        color: AppColors.primaryText,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 1.5),
                    
                    ProfileAvatarWidget(
                      radius: ResponsiveHelper.getValue(context, 70.0, 80.0, 90.0),
                      showEditButton: true,
                    ),
                    
                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 1.5),
                    
                    // Image Status Display
                    Obx(() => Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(context)),
                      decoration: BoxDecoration(
                        color: profileController.selectedProfileImage.value != null
                            ? AppColors.success.withValues(alpha: 0.1)
                            : AppColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(
                          ResponsiveHelper.getResponsiveBorderRadius(context) * 0.8,
                        ),
                        border: Border.all(
                          color: profileController.selectedProfileImage.value != null
                              ? AppColors.success.withValues(alpha: 0.3)
                              : AppColors.primary.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            profileController.selectedProfileImage.value != null
                                ? Icons.check_circle
                                : Icons.info,
                            color: profileController.selectedProfileImage.value != null
                                ? AppColors.success
                                : AppColors.primary,
                          ),
                          SizedBox(width: ResponsiveHelper.getResponsiveSpacing(context) * 0.5),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ResponsiveText(
                                  profileController.selectedProfileImage.value != null
                                      ? 'تم اختيار الصورة بنجاح ✅'
                                      : 'لم يتم اختيار صورة بعد',
                                  baseFontSize: 14,
                                  style: AppStyles.getBodyStyle(context).copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                  textAlign: TextAlign.right,
                                ),
                                if (profileController.selectedProfileImage.value != null) ...[
                                  SizedBox(height: 4),
                                  ResponsiveText(
                                    'اسم الملف: ${profileController.selectedProfileImage.value!.name}',
                                    baseFontSize: 12,
                                    style: AppStyles.getBodyStyle(context).copyWith(
                                      color: AppColors.secondaryText,
                                    ),
                                    textAlign: TextAlign.right,
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ),
                    )),
                  ],
                ),
              ),
              
              SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 2),
              
              // Test Buttons Section
              Container(
                padding: EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(context) * 1.5),
                decoration: BoxDecoration(
                  color: AppColors.cardBackground,
                  borderRadius: BorderRadius.circular(
                    ResponsiveHelper.getResponsiveBorderRadius(context),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primaryText.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      '🧪 أزرار الاختبار',
                      baseFontSize: 18,
                      style: AppStyles.getHeadingStyle(context).copyWith(
                        color: AppColors.primaryText,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.right,
                    ),
                    
                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context)),
                    
                    // Camera Test Button
                    SizedBox(
                      width: double.infinity,
                      child: Obx(() => ElevatedButton.icon(
                        icon: profileController.isImageLoading.value
                            ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                                ),
                              )
                            : const Icon(Icons.camera_alt),
                        label: ResponsiveText(
                          profileController.isImageLoading.value 
                              ? 'جاري فتح الكاميرا...' 
                              : '📷 اختبار الكاميرا',
                          baseFontSize: 16,
                          style: const TextStyle(
                            color: AppColors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: AppColors.white,
                          padding: EdgeInsets.symmetric(
                            vertical: ResponsiveHelper.getResponsiveSpacing(context) * 1.2,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              ResponsiveHelper.getResponsiveBorderRadius(context),
                            ),
                          ),
                        ),
                        onPressed: profileController.isImageLoading.value 
                            ? null 
                            : profileController.pickImageFromCamera,
                      )),
                    ),
                    
                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context)),
                    
                    // Gallery Test Button
                    SizedBox(
                      width: double.infinity,
                      child: Obx(() => ElevatedButton.icon(
                        icon: profileController.isImageLoading.value
                            ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                                ),
                              )
                            : const Icon(Icons.photo_library),
                        label: ResponsiveText(
                          profileController.isImageLoading.value 
                              ? 'جاري فتح المعرض...' 
                              : '🖼️ اختبار المعرض',
                          baseFontSize: 16,
                          style: const TextStyle(
                            color: AppColors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.accent,
                          foregroundColor: AppColors.white,
                          padding: EdgeInsets.symmetric(
                            vertical: ResponsiveHelper.getResponsiveSpacing(context) * 1.2,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              ResponsiveHelper.getResponsiveBorderRadius(context),
                            ),
                          ),
                        ),
                        onPressed: profileController.isImageLoading.value 
                            ? null 
                            : profileController.pickImageFromGallery,
                      )),
                    ),
                    
                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context)),
                    
                    // Clear Image Button
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        icon: const Icon(Icons.clear),
                        label: ResponsiveText(
                          '🗑️ مسح الصورة',
                          baseFontSize: 16,
                          style: TextStyle(
                            color: AppColors.error,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.error,
                          side: const BorderSide(color: AppColors.error),
                          padding: EdgeInsets.symmetric(
                            vertical: ResponsiveHelper.getResponsiveSpacing(context) * 1.2,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              ResponsiveHelper.getResponsiveBorderRadius(context),
                            ),
                          ),
                        ),
                        onPressed: profileController.clearSelectedImage,
                      ),
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 2),
              
              // Test Scenarios Info
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(context) * 1.5),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(
                    ResponsiveHelper.getResponsiveBorderRadius(context),
                  ),
                  border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      '✅ السيناريوهات المُختبرة:',
                      baseFontSize: 16,
                      style: AppStyles.getHeadingStyle(context).copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.right,
                    ),
                    
                    SizedBox(height: ResponsiveHelper.getResponsiveSpacing(context) * 0.8),
                    
                    ResponsiveText(
                      '• ✅ اختيار صورة بنجاح - عرض الصورة وحفظها\n'
                      '• ✅ إلغاء المستخدم - رسالة إعلامية بدون خطأ\n'
                      '• ✅ رفض الأذونات - طلب الأذونات وإرشاد المستخدم\n'
                      '• ✅ أخطاء النظام - رسائل خطأ واضحة ومفيدة\n'
                      '• ✅ تحسين الصور - ضغط تلقائي 800x800 بجودة 80%\n'
                      '• ✅ حالات التحميل - مؤشرات بصرية أثناء المعالجة\n'
                      '• ✅ إدارة الذاكرة - تنظيف تلقائي للموارد',
                      baseFontSize: 14,
                      style: AppStyles.getBodyStyle(context).copyWith(
                        color: AppColors.success,
                        height: 1.6,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}