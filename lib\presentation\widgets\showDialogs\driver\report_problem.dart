import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';

import '../../../../core/theme/app_colors.dart';

void showReportProblemBottomSheet(BuildContext context) => showModalBottomSheet(
  context: context,
  builder: (context) {
    // Always RTL for Arabic-only UI
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Padding(
        padding: EdgeInsets.only(
          top: 16.0,
          left: 16.0,
          right: 16.0,
          bottom: MediaQuery.of(context).viewInsets.bottom + 16.0,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                'الإبلاغ عن مشكلة',
                style: TextStyle(
                  color: Color(0xFF0864B5),
                ),
              ),
            ),
            const Divider(
              color: Colors.black,
              thickness: 1.0,
              indent: 50.0,
              endIndent: 50.0,
            ),
            const SizedBox(height: 10.0),
            _buildFormRow(
              context,
              label: 'سبب المشكلة',
              hintText: 'أدخل سبب المشكلة',
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
            ),
            const SizedBox(height: 10.0),
            _buildFormRow(
              context,
              label: '',
              hintText: '',
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
            ),
            const SizedBox(height: 10.0),
            _buildImpactRow(context, textDirection: TextDirection.rtl),
            const SizedBox(height: 20.0),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  },
);

Widget _buildFormRow(
  BuildContext context, {
  required String label,
  required String hintText,
  required TextDirection textDirection,
  required TextAlign textAlign,
}) {
  return Row(
    children: [
      if (textDirection == TextDirection.rtl) ...[
        Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Color(0xFF4A4A4A),
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14.0),
          ),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: TextFormField(
              textAlign: textAlign,
              textDirection: textDirection,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: TextStyle(color: Colors.grey),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12.0,
                  vertical: 10.0,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(color: Colors.blue),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(color: Colors.blue),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(color: Colors.blue),
                ),
              ),
            ),
          ),
        ),
      ] else ...[
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: TextFormField(
              textAlign: textAlign,
              textDirection: textDirection,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: TextStyle(color: Colors.grey),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12.0,
                  vertical: 10.0,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(color: Colors.blue),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(color: Colors.blue),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(color: Colors.blue),
                ),
              ),
            ),
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Color(0xFF4A4A4A),
            fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14.0),
          ),
        ),
      ],
    ],
  );
}

Widget _buildImpactRow(
  BuildContext context, {
  required TextDirection textDirection,
}) {
  RxString selectedImpact = ''.obs;

  return Obx(
    () => Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Padding(
        //   padding: const EdgeInsets.only(bottom: 12.0),
        //   child: Text(
        //     'problem_impact'.tr,
        //     style: TextStyle(
        //       fontWeight: FontWeight.bold,
        //       color: AppColors.primaryText,
        //       fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14.0),
        //     ),
        //   ),
        // ),
      
          // decoration: BoxDecoration(
          //   border: Border.all(color: Colors.grey.shade200),
          //   borderRadius: BorderRadius.circular(12.0),
          // ),
           Row(
            children: [
              Text(
            'أثر المشكلة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryText,
              fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14.0),
            ),
          ),
          SizedBox(width: 8.0),
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(color: Colors.grey.shade200),
                    ),
                  ),
                  child: RadioListTile<String>(
                    title: Row(
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          color: Colors.green,
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'لا يوجد تأثير',
                            style: TextStyle(
                              fontSize: ResponsiveHelper.getResponsiveFontSize(
                                context,
                                14.0,
                              ),
                              color:
                                  selectedImpact.value == 'no_impact'
                                      ? Colors.green
                                      : Colors.grey[600],
                            ),
                          ),
                        ),
                      ],
                    ),
                    value: 'no_impact',
                    groupValue: selectedImpact.value,
                    activeColor: Colors.green,
                    onChanged: (value) {
                      selectedImpact.value = value!;
                    },
                    contentPadding: EdgeInsets.symmetric(horizontal: 8.0),
                  ),
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: Row(
                    children: [
                      Icon(
                        Icons.warning_amber_rounded,
                        color: Colors.red,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'سيؤثر',
                          style: TextStyle(
                            fontSize: ResponsiveHelper.getResponsiveFontSize(
                              context,
                              14.0,
                            ),
                            color:
                                selectedImpact.value == 'will_impact'
                                    ? Colors.red
                                    : Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                  value: 'will_impact',
                  groupValue: selectedImpact.value,
                  activeColor: Colors.red,
                  onChanged: (value) {
                    selectedImpact.value = value!;
                  },
                  contentPadding: EdgeInsets.symmetric(horizontal: 8.0),
                ),
              ),
            ],
          ),
      
      ],
    ),
  );
}

Widget _buildActionButtons(BuildContext context) {
  return Container(
    margin: EdgeInsets.only(top: 20.0),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                padding: EdgeInsets.symmetric(vertical: 12.0),
              ),
              child: Text(
                'إرسال',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: ResponsiveHelper.getResponsiveFontSize(
                    context,
                    14.0,
                  ),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[300],
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                padding: EdgeInsets.symmetric(vertical: 12.0),
              ),
              child: Text(
                'إلغاء',
                style: TextStyle(
                  color: Colors.black87,
                  fontSize: ResponsiveHelper.getResponsiveFontSize(
                    context,
                    14.0,
                  ),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    ),
  );
}
