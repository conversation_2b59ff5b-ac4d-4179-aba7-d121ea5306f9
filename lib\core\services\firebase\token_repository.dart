import 'package:supabase_flutter/supabase_flutter.dart';

class TokenRepository {
  final SupabaseClient _supabaseClient;

  TokenRepository(this._supabaseClient);

 Future<void> saveDeviceToken({
  required String userId,
  required String token,
  required String role,
}) async {
  try {
    final existingToken = await _supabaseClient
        .from('device_tokens')
        .select()
        .eq('user_id', userId)
        .eq('token', token)
        .maybeSingle();

    if (existingToken != null) {
      // لا نقوم بأي تحديث إذا لم يكن هناك حقل زمني
      print('✅ Token already exists, skipping update');
    } else {
      await _supabaseClient.from('device_tokens').insert({
        'user_id': userId,
        'token': token,
        'role': role,
      });
      print('✅ New token inserted successfully');
    }
  } catch (e) {
    print('❌ Error saving token: $e');
    rethrow;
  }
}
}