class TripModel {
  final int id;
  final int driverId;
  final DateTime startTime;
  final DateTime? endTime;
  final String startLocation;
  final String endLocation;
  final String status;
  final int taskId;
  final int vehicleId;

  TripModel({
    required this.id,
    required this.driverId,
    required this.startTime,
    this.endTime,
    required this.startLocation,
    required this.endLocation,
    required this.status,
    required this.taskId,
    required this.vehicleId,
  });

  factory TripModel.fromJson(Map<String, dynamic> json) {
    return TripModel(
      id: json['id'],
      driverId: json['driver_id'],
      startTime: DateTime.parse(json['start_time']),
      endTime:
          json['end_time'] != null ? DateTime.parse(json['end_time']) : null,
      startLocation: json['start_location'],
      endLocation: json['end_location'],
      status: json['status'],
      taskId: json['task_id'],
      vehicleId: json['vehicle_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'driver_id': driverId,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'start_location': startLocation,
      'end_location': endLocation,
      'status': status,
      'task_id': taskId,
      'vehicle_id': vehicleId,
    };
  }
}
