import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/responsive/driver_cl.dart';
import 'package:driver_app/core/controller/user_controller.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';
import 'package:driver_app/Data/models/task.dart';
import 'package:driver_app/presentation/widgets/showDialogs/driver/task_card.dart';
import '../../../core/routes/app_routes.dart';
import '../../widgets/navigation/navigation_button_bar.dart';

class Dashboard extends StatefulWidget {
  const Dashboard({super.key});

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  int _selectedIndex = 0;
  late Future<List<TaskModel>> _tasksFuture;
  final DriverRepository _driverRepo = DriverRepository();
  final UserController _userController = Get.find<UserController>();
  bool _isLoading = true;

  Future<List<TaskModel>> _loadTasks() async {
    try {
      setState(() => _isLoading = true);

      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.snackbar(
          'جار التحميل',
          'جارٍ تحميل المهام...',
          duration: const Duration(seconds: 2),
        );
      });

      final user = _userController.currentUser;
      print("======================================================$user");
      if (user == null || user.driverDetails == null) {
        throw Exception('User or driver details not available');
      }

      final tasks = await _driverRepo.getDriverTasks(user.driverDetails!.id);

      if (tasks.isEmpty) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.snackbar(
            'معلومات',
            'لا توجد مهام متاحة',
            duration: const Duration(seconds: 2),
          );
        });
      }

      return tasks;
    } catch (e) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.snackbar('خطأ', 'فشل في تحميل المهام: ${e.toString()}');
      });
      return [];
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showTaskDetails(TaskModel task) {
    Get.bottomSheet(
      Container(
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 40,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                task.title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              Text(
                task.description ?? 'لا توجد تفاصيل متاحة',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 20),
              _buildDetailRow(
                Icons.calendar_today,
                'تاريخ الاستحقاق:',
                task.dueDate?.toString() ?? 'غير محدد',
              ),
              _buildDetailRow(
                Icons.location_on,
                'مكان الاستلام:',
                '${task.pickupLocation.coordinates.latitude}, ${task.pickupLocation.coordinates.longitude}',
              ),
              _buildDetailRow(
                Icons.location_on,
                'مكان التسليم:',
                '${task.dropoffLocation.coordinates.latitude}, ${task.dropoffLocation.coordinates.longitude}',
              ),
              _buildDetailRow(Icons.star, 'الحالة:', task.status),
              const SizedBox(height: 30),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    padding: const EdgeInsets.symmetric(vertical: 15),
                  ),
                  onPressed: () => Get.back(),
                  child: Text(
                    'إغلاق',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: AppColors.primary),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Text(value, style: const TextStyle(fontSize: 14)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _tasksFuture = _loadTasks();
  }

  @override
  Widget build(BuildContext context) {
    // Arabic is the only language, always RTL
    const textDirection = TextDirection.rtl;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
         backgroundColor: const Color(0xFF2196F3),
      elevation: 0,
      surfaceTintColor: Colors.transparent,
        title: Text(
          "قائمة المهام",
          style: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontFamily: 'Cairo',
        ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(
            Icons.account_circle,
            color: Colors.black38,
            size: 35,
          ),
          onPressed: () => Get.toNamed(AppRoutes.profile),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.notifications_outlined,
              color: Colors.white,
              size: ResponsiveHelper.getResponsiveIconSize(Get.context!),
            ),
            onPressed: () {},
          ),
        ],
      ),
      bottomNavigationBar: NavigationButtonBar(
        items: [
          NavigationItem(
            icon: Icons.home,
            label: 'لوحة التحكم',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.dashboard),
          ),
          NavigationItem(
            icon: Icons.assignment,
            label: 'سجل النشاط',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () {
              final driverId = _userController.currentUser?.driverDetails?.id;
              if (driverId != null) {
                Get.toNamed(
                  AppRoutes.activityLog,
                  arguments: {'driverId': driverId},
                );
              } else {
                Get.snackbar('خطأ', 'معرف السائق غير متاح');
              }
            },
          ),
          NavigationItem(
            icon: Icons.build,
            label: 'صيانة',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.maintenance),
          ),
          NavigationItem(
            icon: Icons.map,
            label: 'خريطة',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.mapScreen),
          ),
        ],
        currentIndex: _selectedIndex,
        onItemSelected: (index) => setState(() => _selectedIndex = index),
        selectedItemColor: AppColors.primary,
        unselectedItemColor: Colors.grey,
        shadowColor: AppColors.primary.withValues(alpha: 0.1),
      ),
      body: FutureBuilder<List<TaskModel>>(
        future: _tasksFuture,
        builder: (context, snapshot) {
          if (_isLoading) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(
                    'جارٍ تحميل المهام...',
                    style: const TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              ),
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    'فشل في تحميل المهام',
                    style: const TextStyle(fontSize: 18),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    snapshot.error.toString(),
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed:
                        () => setState(() => _tasksFuture = _loadTasks()),
                    child: Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          final tasks = snapshot.data ?? [];

          if (tasks.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.assignment, size: 48, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد مهام مخصصة بعد',
                    style: const TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ليس لديك مهام لعرضها في الوقت الحالي',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed:
                        () => setState(() => _tasksFuture = _loadTasks()),
                    child: Text('تحديث'),
                  ),
                ],
              ),
            );
          }

          return Directionality(
            textDirection: textDirection,
            child: Column(
              children: [
                _buildTasksOverview(context, tasks),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh:
                        () async => setState(() => _tasksFuture = _loadTasks()),
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      itemCount: tasks.length,
                      itemBuilder:
                          (context, index) => GestureDetector(
                            onTap: () => _showTaskDetails(tasks[index]),
                            child: TaskCard(task: tasks[index]),
                          ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  int getTotalTasks(List<TaskModel> tasks) => tasks.length;

  int getCompletedTasks(List<TaskModel> tasks) =>
      tasks.where((t) => t.status == 'completed').length;

  int getIncompleteTasks(List<TaskModel> tasks) =>
      tasks.where((t) => t.status != 'completed').length;

  Widget _buildTasksOverview(
    BuildContext context,
    List<TaskModel> tasks,
  ) {
    final totalTasks = getTotalTasks(tasks);
    final completedTasks = getCompletedTasks(tasks);
    final incompleteTasks = getIncompleteTasks(tasks);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 12),
            Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: BorderSide(color: AppColors.borderColor),
            ),
            child: Column(
              children: [
              _buildAnimatedTaskStatItem(
                context: context,
                title: 'عدد المهام غير المكتملة',
                count: incompleteTasks,
                color: AppColors.error,
                icon: Icons.pending_actions, // أيقونة توضح "غير مكتملة"
              ),
              _buildAnimatedTaskStatItem(
                context: context,
                title: 'عدد المهام المكتملة',
                count: completedTasks,
                color: AppColors.success,
                icon: Icons.verified, // أيقونة توضح "مكتملة"
              ),
              _buildAnimatedTaskStatItem(
                context: context,
                title: 'إجمالي عدد المهام',
                count: totalTasks,
                color: AppColors.secondary,
                icon: Icons.assignment, // أيقونة توضح "إجمالي"
                isLast: true,
              ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedTaskStatItem({
    required BuildContext context,
    required String title,
    required int count,
    required Color color,
    required IconData icon,
    bool isLast = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Color.alphaBlend(
          color.withAlpha(25),
          Theme.of(context).cardColor,
        ),
        border: Border(
          bottom:
              isLast
                  ? BorderSide.none
                  : BorderSide(color: AppColors.borderColor),
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: color),
              const SizedBox(width: 6),
              Text(
                title,
                style: TextStyle(fontWeight: FontWeight.w600, color: color),
              ),
            ],
          ),
          TweenAnimationBuilder(
            tween: IntTween(begin: 0, end: count),
            duration: const Duration(seconds: 1),
            builder:
                (context, value, child) => Text(
                  value.toString(),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: color,
                  ),
                ),
          ),
        ],
      ),
    );
  }
}
