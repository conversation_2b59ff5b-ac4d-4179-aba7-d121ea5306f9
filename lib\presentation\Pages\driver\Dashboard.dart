import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:driver_app/core/responsive/driver_cl.dart';
import 'package:driver_app/core/controller/user_controller.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';
import 'package:driver_app/Data/models/task.dart';
import 'package:driver_app/presentation/widgets/showDialogs/driver/task_card.dart';
import '../../../core/routes/app_routes.dart';
import '../../widgets/navigation/navigation_button_bar.dart';

class Dashboard extends StatefulWidget {
  const Dashboard({super.key});

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  int _selectedIndex = 0;
  late Future<List<TaskModel>> _tasksFuture;
  final DriverRepository _driverRepo = DriverRepository();
  final UserController _userController = Get.find<UserController>();
  bool _isLoading = true;

  Future<List<TaskModel>> _loadTasks() async {
    try {
      setState(() => _isLoading = true);

      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.snackbar(
          'جار التحميل',
          'جارٍ تحميل المهام...',
          duration: const Duration(seconds: 2),
        );
      });

      final user = _userController.currentUser;
      print("======================================================$user");
      if (user == null || user.driverDetails == null) {
        throw Exception('User or driver details not available');
      }

      final tasks = await _driverRepo.getDriverTasks(user.driverDetails!.id);

      if (tasks.isEmpty) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.snackbar(
            'معلومات',
            'لا توجد مهام متاحة',
            duration: const Duration(seconds: 2),
          );
        });
      }

      return tasks;
    } catch (e) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.snackbar('خطأ', 'فشل في تحميل المهام: ${e.toString()}');
      });
      return [];
    } finally {
      setState(() => _isLoading = false);
    }
  }

  String getArabicStatus(String status) {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'inProgress':
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'notStarted':
      case 'pending':
        return 'ملغية';
      default:
        return 'غير معروفة';
    }
  }

  void _showTaskDetails(TaskModel task) {
    Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              decoration: const BoxDecoration(
                color: Color(0xFF64B5F6),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'تفاصيل المهمة',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Get.back(),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Task Name Section
                    _buildTaskNameSection(task),
                    const SizedBox(height: 16),

                    // Date Information Row
                    _buildDateInfoRow(task),
                    const SizedBox(height: 16),

                    // Location Details
                    _buildLocationDetails(task),
                    const SizedBox(height: 16),

                    // Additional Details Button
                    _buildAdditionalDetailsButton(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  Widget _buildTaskNameSection(TaskModel task) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.assignment,
              color: AppColors.primary,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'اسم المهمة',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  task.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateInfoRow(TaskModel task) {
    return Row(
      children: [
        // Creation Date
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: Colors.orange,
                  size: 24,
                ),
                const SizedBox(height: 8),
                const Text(
                  'تاريخ الإنشاء',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  task.createdAt != null
                    ? DateFormat('yyyy/MM/dd').format(task.createdAt!)
                    : 'غير محدد',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 12),
        // Due Date
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.schedule,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(height: 8),
                const Text(
                  'آخر موعد للتسليم',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  task.dueDate != null
                    ? DateFormat('yyyy/MM/dd').format(task.dueDate!)
                    : 'غير محدد',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLocationDetails(TaskModel task) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Pickup Location
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: Colors.green,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'موقع الاستلام',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            task.pickupLocation.name,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // Delivery Location
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: Colors.blue,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'موقع التسليم',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            task.dropoffLocation.name,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // Supervisor
          Row(
            children: [
              Icon(
                Icons.person,
                color: Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'المشرف',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          const Text(
            'عبدالله محمد مكارم',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // Status
          Row(
            children: [
              Icon(
                Icons.info,
                color: Colors.purple,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'حالة المهمة',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            getArabicStatus(task.status),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalDetailsButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.description,
            color: AppColors.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          const Expanded(
            child: Text(
              'تفاصيل إضافية',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: Colors.grey,
            size: 16,
          ),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _tasksFuture = _loadTasks();
  }

  @override
  Widget build(BuildContext context) {
    // Arabic is the only language, always RTL
    const textDirection = TextDirection.rtl;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
         backgroundColor: const Color(0xFF2196F3),
      elevation: 0,
      surfaceTintColor: Colors.transparent,
        title: Text(
          "قائمة المهام",
          style: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontFamily: 'Cairo',
        ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(
            Icons.account_circle,
            color: Colors.black38,
            size: 35,
          ),
          onPressed: () => Get.toNamed(AppRoutes.profile),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.notifications_outlined,
              color: Colors.white,
              size: ResponsiveHelper.getResponsiveIconSize(Get.context!),
            ),
            onPressed: () {},
          ),
        ],
      ),
      bottomNavigationBar: NavigationButtonBar(
        items: [
          NavigationItem(
            icon: Icons.home,
            label: 'لوحة التحكم',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.dashboard),
          ),
          NavigationItem(
            icon: Icons.assignment,
            label: 'سجل النشاط',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () {
              final driverId = _userController.currentUser?.driverDetails?.id;
              if (driverId != null) {
                Get.toNamed(
                  AppRoutes.activityLog,
                  arguments: {'driverId': driverId},
                );
              } else {
                Get.snackbar('خطأ', 'معرف السائق غير متاح');
              }
            },
          ),
          NavigationItem(
            icon: Icons.build,
            label: 'صيانة',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.maintenance),
          ),
          NavigationItem(
            icon: Icons.map,
            label: 'خريطة',
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            onTap: () => Get.toNamed(AppRoutes.mapScreen),
          ),
        ],
        currentIndex: _selectedIndex,
        onItemSelected: (index) => setState(() => _selectedIndex = index),
        selectedItemColor: AppColors.primary,
        unselectedItemColor: Colors.grey,
        shadowColor: AppColors.primary.withValues(alpha: 0.1),
      ),
      body: FutureBuilder<List<TaskModel>>(
        future: _tasksFuture,
        builder: (context, snapshot) {
          if (_isLoading) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(
                    'جارٍ تحميل المهام...',
                    style: const TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              ),
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    'فشل في تحميل المهام',
                    style: const TextStyle(fontSize: 18),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    snapshot.error.toString(),
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed:
                        () => setState(() => _tasksFuture = _loadTasks()),
                    child: Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          final tasks = snapshot.data ?? [];

          if (tasks.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.assignment, size: 48, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد مهام مخصصة بعد',
                    style: const TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ليس لديك مهام لعرضها في الوقت الحالي',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed:
                        () => setState(() => _tasksFuture = _loadTasks()),
                    child: Text('تحديث'),
                  ),
                ],
              ),
            );
          }

          return Directionality(
            textDirection: textDirection,
            child: Column(
              children: [
                _buildTasksOverview(context, tasks),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh:
                        () async => setState(() => _tasksFuture = _loadTasks()),
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      itemCount: tasks.length,
                      itemBuilder:
                          (context, index) => GestureDetector(
                            onTap: () => _showTaskDetails(tasks[index]),
                            child: TaskCard(task: tasks[index]),
                          ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  int getTotalTasks(List<TaskModel> tasks) => tasks.length;

  int getCompletedTasks(List<TaskModel> tasks) =>
      tasks.where((t) => t.status == 'completed').length;

  int getIncompleteTasks(List<TaskModel> tasks) =>
      tasks.where((t) => t.status != 'completed').length;

  Widget _buildTasksOverview(
    BuildContext context,
    List<TaskModel> tasks,
  ) {
    final totalTasks = getTotalTasks(tasks);
    final completedTasks = getCompletedTasks(tasks);
    final incompleteTasks = getIncompleteTasks(tasks);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 12),
            Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: BorderSide(color: AppColors.borderColor),
            ),
            child: Column(
              children: [
              _buildAnimatedTaskStatItem(
                context: context,
                title: 'عدد المهام غير المكتملة',
                count: incompleteTasks,
                color: AppColors.error,
                icon: Icons.pending_actions, // أيقونة توضح "غير مكتملة"
              ),
              _buildAnimatedTaskStatItem(
                context: context,
                title: 'عدد المهام المكتملة',
                count: completedTasks,
                color: AppColors.success,
                icon: Icons.verified, // أيقونة توضح "مكتملة"
              ),
              _buildAnimatedTaskStatItem(
                context: context,
                title: 'إجمالي عدد المهام',
                count: totalTasks,
                color: AppColors.secondary,
                icon: Icons.assignment, // أيقونة توضح "إجمالي"
                isLast: true,
              ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedTaskStatItem({
    required BuildContext context,
    required String title,
    required int count,
    required Color color,
    required IconData icon,
    bool isLast = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Color.alphaBlend(
          color.withAlpha(25),
          Theme.of(context).cardColor,
        ),
        border: Border(
          bottom:
              isLast
                  ? BorderSide.none
                  : BorderSide(color: AppColors.borderColor),
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: color),
              const SizedBox(width: 6),
              Text(
                title,
                style: TextStyle(fontWeight: FontWeight.w600, color: color),
              ),
            ],
          ),
          TweenAnimationBuilder(
            tween: IntTween(begin: 0, end: count),
            duration: const Duration(seconds: 1),
            builder:
                (context, value, child) => Text(
                  value.toString(),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: color,
                  ),
                ),
          ),
        ],
      ),
    );
  }
}
