import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:driver_app/core/controller/user_controller.dart';
import 'package:driver_app/core/services/firebase/token_repository.dart';

class FirebaseApi {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final TokenRepository _tokenRepository;

  FirebaseApi(this._tokenRepository);

  static final navigatorKey = GlobalKey<NavigatorState>();

  Future<void> saveTokenForCurrentUser() async {
    try {
      final token = await _firebaseMessaging.getToken();
      if (token == null) {
        print('⚠️ FCM token is null');
        return;
      }

      print('🔑 Target Token: $token');

      if (Get.isRegistered<UserController>()) {
        final userController = Get.find<UserController>();
        if (userController.user.value != null) {
          await _tokenRepository.saveDeviceToken(
            userId: userController.user.value!.id,
            token: token,
            role: userController.user.value!.role,
          );
          print('✅ Token saved successfully for user ${userController.user.value!.id}');
        } else {
          print('⚠️ No user logged in');
        }
      } else {
        print('⚠️ UserController not registered');
      }
    } catch (e) {
      print('❌ Error saving FCM token: $e');
    }
  }

  Future<void> initNotifications() async {
    try {
      // Request permissions
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      print('🔔 Notification permissions: ${settings.authorizationStatus}');

      // Handle token refresh
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        print('🔄 Token refreshed: $newToken');
        saveTokenForCurrentUser();
      });

      // Save initial token
      await saveTokenForCurrentUser();

      // Setup message handlers
      FirebaseMessaging.onMessage.listen(_showForegroundNotification);
      FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);
      
      // Check initial message
      final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        _handleMessage(initialMessage);
      }

      // Subscribe to topics if needed
      // await _firebaseMessaging.subscribeToTopic('drivers');
      
    } catch (e) {
      print('❌ Error initializing notifications: $e');
    }
  }

  void _handleMessage(RemoteMessage? message) {
    if (message == null) return;
    
    print('📩 Received message: ${message.messageId}');
    
    navigatorKey.currentState?.pushNamed('/notification', arguments: message);
  }

  static Future<void> _showForegroundNotification(RemoteMessage message) async {
    try {
      if (message.notification == null) return;

      print('📢 Showing notification: ${message.notification?.title}');

      const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        'high_importance_channel',
        'High Importance Notifications',
        channelDescription: 'Channel for important notifications',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: true,
      );

      const NotificationDetails platformDetails = NotificationDetails(
        android: androidDetails,
      );

      await _localNotificationsPlugin.show(
        message.hashCode,
        message.notification!.title,
        message.notification!.body,
        platformDetails,
      );
    } catch (e) {
      print('❌ Error showing notification: $e');
    }
  }

  static Future<void> initializeLocalNotifications() async {
    try {
      const AndroidInitializationSettings androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      const InitializationSettings settings =
          InitializationSettings(android: androidSettings);

      await _localNotificationsPlugin.initialize(
        settings,
        onDidReceiveNotificationResponse: (NotificationResponse details) {
          // Handle notification tap
          print('👆 Notification tapped: ${details.payload}');
        },
      );
    } catch (e) {
      print('❌ Error initializing local notifications: $e');
    }
  }

  // Add this method to handle token unregistration
  Future<void> unregisterToken() async {
    try {
      await _firebaseMessaging.deleteToken();
      print('🗑️ FCM token deleted');
    } catch (e) {
      print('❌ Error deleting FCM token: $e');
    }
  }
}