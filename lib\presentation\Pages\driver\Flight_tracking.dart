import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:driver_app/core/controller/user_controller.dart';
import 'package:driver_app/core/routes/app_routes.dart';
import 'package:driver_app/core/routes/consid_map.dart';
import 'package:driver_app/core/services/auth_service.dart';
import 'package:driver_app/core/services/firebase/send_notification.dart';

class FlightTrackingScreen extends StatefulWidget {
  final int tripId;
  final String taskId;
  final LatLng pickupLocation;
  final LatLng dropoffLocation;

  const FlightTrackingScreen({
    super.key,
    required this.taskId,
    required this.pickupLocation,
    required this.dropoffLocation,
    required this.tripId,
  });

  @override
  State<FlightTrackingScreen> createState() => _FlightTrackingScreenState();
}

class _FlightTrackingScreenState extends State<FlightTrackingScreen> {
  final Completer<GoogleMapController> _mapController = Completer();
  final Location _location = Location();
  final SupabaseClient _supabase = Supabase.instance.client;
  final userController = Get.find<UserController>();
  LatLng? _currentPosition;
  Map<PolylineId, Polyline> polylines = {};
  bool reachedPickup = false;
  bool reachedDropoff = false;

  double _distanceInMeters = 0;
  double _estimatedTimeInMinutes = 0;
  double _currentSpeed = 0;

  Timer? _timer;
  int _countdownSeconds = 0;
  Timer? _locationUpdateTimer; // مؤقت لتحديث الموقع في قاعدة البيانات

  static const double proximityThreshold = 50; // متر
  static const Duration locationUpdateInterval = Duration(
    seconds: 10,
  ); // تحديث الموقع كل 10 ثواني

  @override
  void initState() {
    super.initState();
    getLocationUpdates();
    _startLocationUpdates(); // بدء تحديث الموقع في قاعدة البيانات
  }

  @override
  void dispose() {
    _timer?.cancel();
    _locationUpdateTimer?.cancel(); // إيقاف مؤقت تحديث الموقع
    super.dispose();
  }

  // بدء تحديث الموقع في قاعدة البيانات
  void _startLocationUpdates() {
    _locationUpdateTimer = Timer.periodic(locationUpdateInterval, (timer) {
      if (_currentPosition != null) {
        _updateDriverLocation();
      }
    });
  }

  // تحديث موقع السائق في قاعدة البيانات
  Future<void> _updateDriverLocation() async {
    final driverId = userController.currentUser?.driverDetails?.id;
    if (driverId == null) return;

    try {
      await _supabase.from('driver_locations').upsert({
        'driver_id': driverId,
        'latitude': _currentPosition!.latitude,
        'longitude': _currentPosition!.longitude,
        'updated_at': DateTime.now().toIso8601String(),
      });
      print(
        'تم تحديث موقع السائق: ${_currentPosition!.latitude}, ${_currentPosition!.longitude}',
      );
    } catch (e) {
      print('خطأ في تحديث موقع السائق: $e');
    }
  }

  void getLocationUpdates() async {
    bool serviceEnabled = await _location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await _location.requestService();
      if (!serviceEnabled) return;
    }

    PermissionStatus permissionGranted = await _location.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await _location.requestPermission();
      if (permissionGranted != PermissionStatus.granted) return;
    }

    _location.onLocationChanged.listen((locationData) async {
      if (locationData.latitude != null && locationData.longitude != null) {
        LatLng newPosition = LatLng(
          locationData.latitude!,
          locationData.longitude!,
        );

        setState(() {
          _currentPosition = newPosition;
          _currentSpeed = locationData.speed ?? 0;
        });

        _moveCamera(newPosition);

        if (!reachedPickup && _isNear(newPosition, widget.pickupLocation)) {
          reachedPickup = true;
          await _updateTaskStatus("in_progress");
          _drawPolyline(widget.pickupLocation, widget.dropoffLocation);
        } else if (reachedPickup &&
            !reachedDropoff &&
            _isNear(newPosition, widget.dropoffLocation)) {
          reachedDropoff = true;
          await _updateTaskStatus("completed");
          await _updateTripStatus();
          _showCompletionDialog();
          sendtoNotification(
            'انتهت الرحلة بنجاح ',
            '${userController.currentUser?.fullName} أكمل رحلته و انجاز مهمته بنجاح',
          );
        }

        if (!reachedPickup) {
          _drawPolyline(newPosition, widget.pickupLocation);
        }

        if (!reachedDropoff) {
          double distance = _calculateDistance(
            newPosition.latitude,
            newPosition.longitude,
            reachedPickup
                ? widget.dropoffLocation.latitude
                : widget.pickupLocation.latitude,
            reachedPickup
                ? widget.dropoffLocation.longitude
                : widget.pickupLocation.longitude,
          );

          double actualSpeed = locationData.speed ?? 0;
          double estimatedTime;

          if (actualSpeed > 1) {
            estimatedTime = (distance / actualSpeed) / 60;
          } else {
            double defaultSpeed = 13.88; // 50 كم/س
            estimatedTime = (distance / defaultSpeed) / 60;
          }

          setState(() {
            _distanceInMeters = distance;
            _estimatedTimeInMinutes = estimatedTime;
            _countdownSeconds = (estimatedTime * 60).toInt();
          });

          _startCountdown();
        }
      }
    });
  }

  void _startCountdown() {
    _timer?.cancel();
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (_countdownSeconds > 0) {
        setState(() {
          _countdownSeconds--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  bool _isNear(LatLng a, LatLng b) {
    double distance = _calculateDistance(
      a.latitude,
      a.longitude,
      b.latitude,
      b.longitude,
    );
    return distance < proximityThreshold;
  }

  double _calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    const double R = 6371000; // متر
    double dLat = _degToRad(lat2 - lat1);
    double dLon = _degToRad(lon2 - lon1);
    double a =
        (sin(dLat / 2) * sin(dLat / 2)) +
        cos(_degToRad(lat1)) *
            cos(_degToRad(lat2)) *
            (sin(dLon / 2) * sin(dLon / 2));
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return R * c;
  }

  double _degToRad(double deg) => deg * (pi / 180);

  Future<void> _updateTaskStatus(String status) async {
    await _supabase
        .from('tasks')
        .update({'status': status})
        .eq('id', widget.taskId);
    final authService = Get.find<AuthService>();
    await authService.updateDriverStatus('active');
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      builder:
          (_) => AlertDialog(
            title: Text("تم إكمال المهمة ✅"),
            content: Text("لقد وصلت إلى نقطة التسليم."),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text("حسنًا"),
              ),
            ],
          ),
    );
  }

  Future<void> _moveCamera(LatLng position) async {
    final GoogleMapController controller = await _mapController.future;
    CameraPosition newPosition = CameraPosition(target: position, zoom: 15);
    await controller.animateCamera(CameraUpdate.newCameraPosition(newPosition));
  }

  Future<void> _drawPolyline(LatLng from, LatLng to) async {
    PolylinePoints polylinePoints = PolylinePoints();
    List<LatLng> polylineCoordinates = [];

    PolylineResult result = await polylinePoints.getRouteBetweenCoordinates(
      googleApiKey: googles,
      request: PolylineRequest(
        origin: PointLatLng(from.latitude, from.longitude),
        destination: PointLatLng(to.latitude, to.longitude),
        mode: TravelMode.driving,
      ),
    );

    if (result.points.isNotEmpty) {
      polylineCoordinates =
          result.points.map((p) => LatLng(p.latitude, p.longitude)).toList();
    }

    PolylineId id = PolylineId("route");
    Polyline polyline = Polyline(
      polylineId: id,
      color: Colors.blueAccent,
      width: 5,
      points: polylineCoordinates,
    );

    setState(() {
      polylines.clear();
      polylines[id] = polyline;
    });
  }

  String _formatCountdown() {
    int minutes = _countdownSeconds ~/ 60;
    int seconds = _countdownSeconds % 60;
    return minutes > 0
        ? "$minutes دقيقة ${seconds.toString().padLeft(2, '0')} ثانية"
        : "وصلت تقريبًا";
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        // appBar: AppBar(title: Text('Trip #${widget.tripId}')),
        
        body:
            _currentPosition == null
                ? const Center(child: CircularProgressIndicator())
                : Stack(
                  children: [
                    GoogleMap(
                      onMapCreated:
                          (controller) => _mapController.complete(controller),
                      initialCameraPosition: CameraPosition(
                        target: _currentPosition!,
                        zoom: 15,
                      ),
                      markers: {
                        Marker(
                          markerId: MarkerId("current_location"),
                          position: _currentPosition!,
                          icon: BitmapDescriptor.defaultMarkerWithHue(
                            BitmapDescriptor.hueAzure,
                          ),
                        ),
                        Marker(
                          markerId: MarkerId("pickup_location"),
                          position: widget.pickupLocation,
                          icon: BitmapDescriptor.defaultMarkerWithHue(
                            BitmapDescriptor.hueGreen,
                          ),
                        ),
                        Marker(
                          markerId: MarkerId("dropoff_location"),
                          position: widget.dropoffLocation,
                          icon: BitmapDescriptor.defaultMarkerWithHue(
                            BitmapDescriptor.hueRed,
                          ),
                        ),
                      },
                      polylines: Set<Polyline>.of(polylines.values),
                    ),
                    Positioned(
                      top: 40,
                      left: 20,
                      right: 20,
                      child: Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 6,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              "🚗 السرعة الحالية: ${_currentSpeed.toStringAsFixed(1)} م/ث",
                              style: TextStyle(fontSize: 16),
                            ),
                            SizedBox(height: 4),
                            Text(
                              "📍 المسافة المتبقية: ${(_distanceInMeters / 1000).toStringAsFixed(2)} كم",
                              style: TextStyle(fontSize: 16),
                            ),
                            SizedBox(height: 4),
                            Text(
                              "⏳ الوقت المتبقي: ${_formatCountdown()}",
                              style: TextStyle(
                                fontSize: 16,
                                color:
                                    _countdownSeconds <= 300
                                        ? Colors.red
                                        : Colors.blue,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              "🕒 الوقت المقدر للوصول: ${_estimatedTimeInMinutes.toStringAsFixed(2)} دقيقة",
                              style: TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 40,
                      left: 20,
                      child: ElevatedButton(
      // <<<<<<< HEAD
              onPressed: () {
                _showReportIssueDialog();
                sendtoNotification(
        'مشكلة في الرحلة',
        '${userController.currentUser?.fullName} واجه مشكلة في الرحلة',
      );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              child: Text("ابلاغ عن المشكلة"),
                      ),
                    ),
                  ],
                ),
      ),
    );
  }

  Future<void> _updateTripStatus() async {
    await _supabase
        .from('trips')
        .update({
          'status': 'completed',
          'end_time': DateTime.now().toIso8601String(),
        })
        .eq('id', widget.tripId);
    _logActivity(
      'Update Trip and Task Status',
      'Trip and Task status updated to completed.',
    );

    // إيقاف مؤقت تحديث الموقع عند انتهاء الرحلة
    _locationUpdateTimer?.cancel();

    if (mounted) {
      Get.snackbar(
        'نجاح',
        'تم إنهاء الرحلة بنجاح! ✅',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    }
  }

  void _showReportIssueDialog() {
    String issueType = 'تأخير';
    String description = '';
    bool isAffectingTrip = false;
    final driverId = userController.currentUser?.driverDetails?.id;

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: Text('الإبلاغ عن مشكلة'),
                  content: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        DropdownButtonFormField<String>(
                          value: issueType,
                          items:
                              [
                                'تأخير',
                                'مشكلة مرورية',
                                'مشكلة في المركبة',
                                'مشكلة في الطريق',
                                'حادث',
                              ].map((String value) {
                                return DropdownMenuItem<String>(
                                  value: value,
                                  child: Text(value),
                                );
                              }).toList(),
                          onChanged:
                              (value) => setState(() => issueType = value!),
                          decoration: InputDecoration(labelText: 'نوع المشكلة'),
                        ),
                        SizedBox(height: 16),
                        TextField(
                          decoration: InputDecoration(
                            labelText: 'وصف المشكلة',
                            border: OutlineInputBorder(),
                          ),
                          maxLines: 3,
                          onChanged: (value) => description = value,
                        ),
                        SizedBox(height: 16),
                        Text('هل تؤثر على الرحلة؟'),
                        Column(
                          children: [
                            RadioListTile<bool>(
                              title: Text('نعم'),
                              value: true,
                              groupValue: isAffectingTrip,
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() => isAffectingTrip = value);
                                }
                              },
                            ),
                            RadioListTile<bool>(
                              title: Text('لا'),
                              value: false,
                              groupValue: isAffectingTrip,
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() => isAffectingTrip = value);
                                }
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('إلغاء'),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        if (description.isEmpty) {
                          Get.snackbar('خطأ', 'يرجى إدخال وصف المشكلة');
                          return;
                        }
                        if (isAffectingTrip) {
                          try {
                            _logActivity(
                              'Report Issue',
                              'Driver reported an issue affecting the trip.',
                            );
                            await _supabase.from('trip_issues').insert({
                              'issue_type': issueType,
                              'description': description,
                              'reported_at': DateTime.now().toIso8601String(),
                              'trip_id': widget.tripId,
                              'is_affecting_trip': isAffectingTrip,
                              'reported_by': driverId,
                            });
                            final authService = Get.find<AuthService>();
                            await authService.updateDriverStatus('active');

                            await _supabase
                                .from('trips')
                                .update({
                                  'status': 'canseled',
                                  'end_time': DateTime.now().toIso8601String(),
                                })
                                .eq('id', widget.tripId);

                            await _supabase
                                .from('tasks')
                                .update({'status': 'canseled'})
                                .eq('id', widget.taskId);
sendtoNotification(
            'تم الغاء المهمة',
            '${userController.currentUser?.fullName} صادفته مشكلة طارئة',
          );
                            Get.snackbar(
                              'تم الإبلاغ',
                              'تم تحديث حالة المهمة و الرحلة الى canseled',
                              backgroundColor: Colors.green,
                              colorText: Colors.white,
                            );
                            Navigator.pop(context);
                            Get.offAllNamed(AppRoutes.dashboard);
                          } catch (e) {
                            Get.snackbar('خطأ', 'فشل في تسجيل المشكلة: $e');
                            print('Error reporting issue: $e');
                          }
                        } else {
                          Get.snackbar(
                            'تم الإبلاغ',
                            'تم تسجيل المشكلة بنجاح',
                            backgroundColor: Colors.green,
                            colorText: Colors.white,
                          );
                          Navigator.pop(context);
                        }
                      },
                      child: Text('إرسال'),
                    ),
                  ],
                ),
          ),
    );
  }

  Future<void> _logActivity(String action, String description) async {
    try {
      final supabase = Supabase.instance.client;
      final userController = Get.find<UserController>();
      final user = userController.user.value;
      if (user == null || user.driverDetails == null) return;

      // الحصول على user_id من جدول السائقين
      final driverResponse =
          await supabase
              .from('drivers')
              .select('user_id')
              .eq('id', user.driverDetails!.id)
              .single();

      final userId = driverResponse['user_id'];
      final fullDescription = '${user.fullName} - $description';

      await supabase.from('activity_logs').insert({
        'user_id': userId,
        'action': action,
        'description': fullDescription,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('فشل في تسجيل النشاط: $e');
    }
  }

  Future<void> sendtoNotification(String title, String des) async {
    final supabase = Supabase.instance.client;
    try {
      print("====================================================");

      final supervisorID =
          await supabase
              .from('drivers')
              .select('supervisor_id')
              .eq('id', userController.currentUser?.driverDetails!.id ?? '')
              .single();

      final supervisorIdValue = supervisorID['supervisor_id'];
      print('Supervisor ID: $supervisorIdValue');

      final supervisorTokenRes = await supabase
          .from('supervisors')
          .select('user_id')
          .eq('id', supervisorIdValue);

      final supervisorUserId = supervisorTokenRes.first['user_id'];
      print('Supervisor User ID: $supervisorUserId');

      final tokenRes = await supabase
          .from('device_tokens')
          .select('token')
          .eq('user_id', supervisorUserId);

      final targetToken = tokenRes.first['token'];
      print('Target Token: $targetToken');
      print("====================================================");
      sendFCMv1Notification(
        targetToken:
            targetToken.toString(),
        title: title,
        body: des,
      );
      sendFCMv1Notification(
        targetToken:'cU2dMz64SPiKYVpTgSYer0:APA91bGgFGqBiE3ajx4-CR7aJYX6yHkSwGevBzbWg4Hp3UKQe3_DJBIk1dxsihNXvIO3eQoEEcknwj7MlN3lmjor_hjxwDlLVlp7WU6u8h-4urYyMbx4Wtw',
        title: title,
        body: des,
      );

    } catch (e) {
      print(e.toString());
    }
  }
}
