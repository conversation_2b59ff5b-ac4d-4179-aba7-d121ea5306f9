import 'package:flutter/material.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';

class AppStyles {
  static TextStyle getHeadingStyle(BuildContext context) {
    return TextStyle(
      fontSize: ResponsiveHelper.getResponsiveFontSize(context, 24),
      fontWeight: FontWeight.bold,
      color: AppColors.primaryText,
      fontFamily: 'Cairo',
    );
  }

  static TextStyle getSubheadingStyle(BuildContext context) {
    return TextStyle(
      fontSize: ResponsiveHelper.getResponsiveFontSize(context, 18),
      fontWeight: FontWeight.w600,
      color: AppColors.primaryText,
      fontFamily: 'Cairo',
    );
  }

  static TextStyle getBodyStyle(BuildContext context) {
    return TextStyle(
      fontSize: ResponsiveHelper.getResponsiveFontSize(context, 16),
      color: AppColors.primaryText,
      fontFamily: 'Cairo',
    );
  }

  static TextStyle getCaptionStyle(BuildContext context) {
    return TextStyle(
      fontSize: ResponsiveHelper.getResponsiveFontSize(context, 14),
      color: AppColors.secondaryText,
      fontFamily: 'Cairo',
    );
  }

  static ButtonStyle getPrimaryButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.white,
      padding: EdgeInsets.symmetric(
        vertical: ResponsiveHelper.isMobile(context) ? 12 : 16,
        horizontal: ResponsiveHelper.isMobile(context) ? 24 : 32,
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      textStyle: TextStyle(
        fontSize: ResponsiveHelper.getResponsiveFontSize(context, 16),
        fontWeight: FontWeight.bold,
        fontFamily: 'Cairo',
      ),
    );
  }

  static double getSpacing(BuildContext context) {
    if (ResponsiveHelper.isMobile(context)) {
      return 16.0;
    } else if (ResponsiveHelper.isTablet(context)) {
      return 24.0;
    } else {
      return 32.0;
    }
  }

  static InputDecoration getInputDecoration(
    BuildContext context, {
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    String? errorText,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      errorText: errorText,
      contentPadding: EdgeInsets.symmetric(
        vertical: ResponsiveHelper.isMobile(context) ? 12 : 16,
        horizontal: ResponsiveHelper.isMobile(context) ? 16 : 20,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.borderColor, width: 1),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.borderColor, width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.error, width: 1),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.error, width: 2),
      ),
      labelStyle: getCaptionStyle(
        context,
      ).copyWith(color: AppColors.secondaryText),
      hintStyle: getCaptionStyle(context).copyWith(color: AppColors.hintText),
      errorStyle: getCaptionStyle(context).copyWith(color: AppColors.error),
      alignLabelWithHint: true,
      floatingLabelBehavior: FloatingLabelBehavior.auto,
    );
  }
}
