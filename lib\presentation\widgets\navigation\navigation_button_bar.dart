import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';
import 'package:driver_app/core/responsive/responsive_text.dart';

class NavigationItem {
  final IconData icon;
  final String label;
  final Color activeColor;
  final Color inactiveColor;
  final VoidCallback? onTap;

  const NavigationItem({
    required this.icon,
    required this.label,
    required this.activeColor,
    required this.inactiveColor,
    this.onTap,
  });
}
class ItemAnimationProperties {
  final Duration duration;
  final Curve curve;

  const ItemAnimationProperties({
    required this.duration,
    required this.curve,
  });
}

class NavigationButtonBar extends StatefulWidget {
  final List<NavigationItem> items;
  final int currentIndex;
  final Function(int) onItemSelected;
  final bool isHorizontal;
  final Color backgroundColor;
  final Color selectedItemColor;
  final Color unselectedItemColor;
  final bool showLabels;
  final bool showProfileAvatar;
  final String? profileImagePath;
  final String? userName;
  final String? userEmail;
  final Duration animationDuration;
  final Curve animationCurve;
  final ItemAnimationProperties itemAnimationProperties;
  final double elevation;
  final Color shadowColor;

  const NavigationButtonBar({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onItemSelected,
    this.isHorizontal = true,
    this.backgroundColor = Colors.white,
    required this.selectedItemColor,
    required this.unselectedItemColor,
    this.showLabels = true,
    this.showProfileAvatar = false,
    this.profileImagePath,
    this.userName,
    this.userEmail,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.itemAnimationProperties = const ItemAnimationProperties(
      duration: Duration(milliseconds: 200),
      curve: Curves.easeInOut,
    ),
    this.elevation = 8.0,
    required this.shadowColor,
  });

  @override
  State<NavigationButtonBar> createState() => _NavigationButtonBarState();
}

class _NavigationButtonBarState extends State<NavigationButtonBar>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _initializeAnimations();
    _controller.forward();
  }

  void _initializeAnimations() {
    _animations = List.generate(
      widget.items.length,
      (index) => Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(
            index * 0.1,
            0.5 + index * 0.1,
            curve: Curves.easeInOut,
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isRTL = true; // Always RTL for Arabic-only UI
    return widget.isHorizontal 
        ? _buildHorizontalNavBar(context, isRTL)
        : _buildVerticalNavBar(context, isRTL);
  }

  Widget _buildHorizontalNavBar(BuildContext context, bool isRTL) {
    return AnimatedContainer(
      duration: widget.animationDuration,
      curve: widget.animationCurve,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        boxShadow: [
          BoxShadow(
            color: widget.shadowColor,
            blurRadius: widget.elevation,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(widget.items.length, (index) {
              final item = widget.items[index];
              final isSelected = index == widget.currentIndex;

              return Flexible(
                child: AnimatedContainer(
                  duration: widget.itemAnimationProperties.duration,
                  curve: widget.itemAnimationProperties.curve,
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? widget.selectedItemColor.withValues(alpha: 0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: InkWell(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      item.onTap?.call();
                      widget.onItemSelected(index);
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8.0,
                        vertical: 8.0,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          AnimatedScale(
                            scale: isSelected ? 1.2 : 1.0,
                            duration: widget.itemAnimationProperties.duration,
                            curve: widget.itemAnimationProperties.curve,
                            child: Icon(
                              item.icon,
                              color: isSelected
                                  ? item.activeColor
                                  : item.inactiveColor,
                            ),
                          ),
                          if (widget.showLabels) ...[
                            const SizedBox(height: 4),
                            AnimatedDefaultTextStyle(
                              duration: widget.itemAnimationProperties.duration,
                              curve: widget.itemAnimationProperties.curve,
                              style: TextStyle(
                                color: isSelected
                                    ? item.activeColor
                                    : item.inactiveColor,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                fontSize: isSelected ? 12 : 10,
                              ),
                              child: Text(
                                item.label,
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
      ),
    );
  }

  Widget _buildVerticalNavBar(BuildContext context, bool isRTL) {
    final double drawerWidth = ResponsiveHelper.getValue(
      context,
      MediaQuery.of(context).size.width * 0.7,
      MediaQuery.of(context).size.width * 0.4,
      MediaQuery.of(context).size.width * 0.3,
    );
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: drawerWidth,
      color: widget.backgroundColor,
      child: SafeArea(
        child: Column(
          children: [
            if (widget.showProfileAvatar) _buildProfileHeader(context),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: _buildNavigationItems(
                  context, 
                  false,
                  ResponsiveHelper.getResponsiveIconSize(context),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context) {
    final double avatarRadius = ResponsiveHelper.getValue(context, 40.0, 50.0, 60.0);
    final double padding = ResponsiveHelper.getResponsivePadding(context);
    
    return Hero(
      tag: 'profile_header',
      child: Container(
        padding: EdgeInsets.all(padding),
        decoration: const BoxDecoration(
          color: AppColors.logoColor,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TweenAnimationBuilder<double>(
              tween: Tween(begin: 0.0, end: 1.0),
              duration: const Duration(milliseconds: 500),
              builder: (context, value, child) {
                return Transform.scale(
                  scale: value,
                  child: child,
                );
              },
              child: CircleAvatar(
                backgroundImage: AssetImage(widget.profileImagePath ?? 'assets/images/logo2.png'),
                radius: avatarRadius,
              ),
            ),
            SizedBox(height: padding / 2),
            if (widget.userName != null)
              _buildAnimatedText(
                widget.userName!,
                ResponsiveHelper.getValue(context, 16.0, 18.0, 20.0),
                FontWeight.bold,
              ),
            if (widget.userEmail != null)
              _buildAnimatedText(
                widget.userEmail!,
                ResponsiveHelper.getValue(context, 12.0, 14.0, 16.0),
                FontWeight.normal,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedText(String text, double fontSize, FontWeight weight) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 500),
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, (1 - value) * 20),
            child: ResponsiveText(
              text,
              baseFontSize: fontSize,
              style: TextStyle(
                color: Colors.black,
                fontWeight: weight,
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildNavigationItems(BuildContext context, bool isHorizontal, double iconSize) {
    final double spacing = ResponsiveHelper.getResponsiveSpacing(context);
    
    return List.generate(widget.items.length, (index) {
      final item = widget.items[index];
      final isSelected = index == widget.currentIndex;
      
      if (isHorizontal) {
        return ScaleTransition(
          scale: _animations[index],
          child: InkWell(
            onTap: () {
              HapticFeedback.lightImpact();
              widget.onItemSelected(index);
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: EdgeInsets.all(spacing / 2),
                  decoration: BoxDecoration(
                    color: isSelected ? widget.selectedItemColor.withValues(alpha: 0.1) : Colors.transparent,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    item.icon,
                    color: isSelected ? item.activeColor : item.inactiveColor,
                    size: iconSize,
                  ),
                ),
                if (widget.showLabels) ...[
                  SizedBox(height: spacing / 2),
                  AnimatedDefaultTextStyle(
                    duration: const Duration(milliseconds: 200),
                    style: TextStyle(
                      color: isSelected ? item.activeColor : item.inactiveColor,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      fontSize: ResponsiveHelper.getValue(context, 10.0, 12.0, 14.0),
                    ),
                    child: Text(item.label),
                  ),
                ],
              ],
            ),
          ),
        );
      } else {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          margin: EdgeInsets.symmetric(
            horizontal: ResponsiveHelper.getResponsivePadding(context) / 2,
            vertical: spacing / 4,
          ),
          decoration: BoxDecoration(
            color: isSelected ? widget.selectedItemColor.withValues(alpha: 0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
          ),
          child: ListTile(
            contentPadding: EdgeInsets.symmetric(
              horizontal: ResponsiveHelper.getResponsivePadding(context),
              vertical: spacing / 2,
            ),
            leading: Icon(
              item.icon,
              color: isSelected ? item.activeColor : item.inactiveColor,
              size: iconSize,
            ),
            title: ResponsiveText(
              item.label,
              baseFontSize: ResponsiveHelper.getValue(context, 14.0, 16.0, 18.0),
              style: TextStyle(
                color: isSelected ? item.activeColor : item.inactiveColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            selected: isSelected,
            onTap: () {
              HapticFeedback.lightImpact();
              widget.onItemSelected(index);
            },
          ),
        );
      }
    });
  }
}