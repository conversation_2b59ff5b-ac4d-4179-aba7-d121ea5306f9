import 'package:flutter/material.dart';

class AppColors {
  // Primary Brand Colors
  static const Color primary = Color(0xFF64B5F6);
  static const Color secondary = Color.fromRGBO(66, 165, 245, 1);
  static const Color accent = Color(0xFF2196F3);

  // Material Design 3 Surface Colors
  static const Color surface = Color(0xFFFFFBFE);
  static const Color surfaceVariant = Color(0xFFF3F0F4);
  static const Color onSurface = Color(0xFF1C1B1F);
  static const Color onSurfaceVariant = Color(0xFF49454F);
  static const Color outline = Color(0xFF79747E);
  static const Color outlineVariant = Color(0xFFCAC4D0);

  // Text Colors
  static const Color primaryText = Color(0xFF333333);
  static const Color secondaryText = Color(0xFF757575);
  static const Color hintText = Color.fromRGBO(189, 189, 189, 1);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFFFFFFFF);

  // Status Colors
  static const Color statusNew = Colors.green;
  static const Color statusInProgress = Colors.orange;
  static const Color statusInactive = Colors.grey;
  static const Color statusCompleted = Colors.blue;
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFE53935);
  static const Color warning = Colors.orange;

  // Background Colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color cardBackground = Colors.white;
  static const Color lightGrey = Color(0xFFEEEEEE);
  static const Color grey50 = Color(0xFFFAFAFA);
  static const Color grey100 = Color(0xFFF5F5F5);
  static const Color grey200 = Color(0xFFEEEEEE);
  static const Color grey300 = Color(0xFFE0E0E0);
  static const Color grey400 = Color(0xFFBDBDBD);
  static const Color grey500 = Color(0xFF9E9E9E);

  // Border and Divider Colors
  static const Color borderColor = Color(0xFF64B5Ff);
  static const Color dividerColor = Colors.grey;
  static const Color activeColor = Colors.green;

  // Basic Colors
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color transparent = Colors.transparent;

  // Icon Colors
  static const Color secondaryIconColor = Color(0xFF757575);
  static const Color logoColor = Color(0xFF64B5F6);

  // Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFF64B5F6),
    Color(0xFF42A5F5),
    Color(0xFF2196F3),
  ];

  static const List<Color> backgroundGradient = [
    Color(0xFFF8FBFF),
    Color(0xFFE3F2FD),
    Color(0xFFBBDEFB),
  ];

  // Validation Colors
  static const Color validGreen = Color(0xFF4CAF50);
  static const Color invalidRed = Color(0xFFE53935);

  // Helper method for opacity
  static Color withValues({required Color color, double alpha = 1.0}) {
    return color.withValues(alpha: alpha);
  }

  // Helper method for getting theme-appropriate colors
  static Color getOnSurface(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark 
        ? Colors.white 
        : onSurface;
  }

  static Color getSurface(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark 
        ? const Color(0xFF121212) 
        : surface;
  }
}