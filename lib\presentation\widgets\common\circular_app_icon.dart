import 'package:flutter/material.dart';
import 'package:driver_app/core/theme/app_colors.dart';
import 'package:driver_app/core/responsive/responsive_helper.dart';

/// أداة عرض أيقونة التطبيق الدائرية
class CircularAppIcon extends StatelessWidget {
  final double? radius;
  final bool showBorder;
  final Color? borderColor;
  final double? borderWidth;
  final bool showShadow;
  final String? imagePath;

  const CircularAppIcon({
    super.key,
    this.radius,
    this.showBorder = true,
    this.borderColor,
    this.borderWidth,
    this.showShadow = true,
    this.imagePath,
  });

  @override
  Widget build(BuildContext context) {
    final double iconRadius = radius ?? ResponsiveHelper.getValue(context, 30.0, 35.0, 40.0);
    final Color finalBorderColor = borderColor ?? AppColors.primary;
    final double finalBorderWidth = borderWidth ?? 2.0;

    return Container(
      decoration: showShadow
          ? BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                  spreadRadius: 1,
                ),
              ],
            )
          : null,
      child: CircleAvatar(
        radius: iconRadius + (showBorder ? finalBorderWidth : 0),
        backgroundColor: showBorder ? finalBorderColor : Colors.transparent,
        child: CircleAvatar(
          radius: iconRadius,
          backgroundImage: AssetImage(imagePath ?? 'assets/images/driver.jpeg'),
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          onBackgroundImageError: (exception, stackTrace) {
            // في حالة فشل تحميل الصورة، استخدم لون افتراضي
          },
          child: imagePath == null
              ? Icon(
                  Icons.local_shipping,
                  size: iconRadius * 0.6,
                  color: AppColors.primary,
                )
              : null,
        ),
      ),
    );
  }
}

/// أداة عرض أيقونة التطبيق الدائرية مع نص
class CircularAppIconWithLabel extends StatelessWidget {
  final double? radius;
  final String? label;
  final TextStyle? labelStyle;
  final bool showBorder;
  final Color? borderColor;
  final double? borderWidth;
  final bool showShadow;
  final String? imagePath;
  final double spacing;

  const CircularAppIconWithLabel({
    super.key,
    this.radius,
    this.label,
    this.labelStyle,
    this.showBorder = true,
    this.borderColor,
    this.borderWidth,
    this.showShadow = true,
    this.imagePath,
    this.spacing = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CircularAppIcon(
          radius: radius,
          showBorder: showBorder,
          borderColor: borderColor,
          borderWidth: borderWidth,
          showShadow: showShadow,
          imagePath: imagePath,
        ),
        if (label != null) ...[
          SizedBox(height: spacing),
          Text(
            label!,
            style: labelStyle ??
                TextStyle(
                  fontSize: ResponsiveHelper.getResponsiveFontSize(context, 12),
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryText,
                  fontFamily: 'Cairo',
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// أداة أيقونة التطبيق كزر قابل للضغط
class CircularAppIconButton extends StatelessWidget {
  final double? radius;
  final VoidCallback? onTap;
  final bool showBorder;
  final Color? borderColor;
  final double? borderWidth;
  final bool showShadow;
  final String? imagePath;
  final bool showRipple;

  const CircularAppIconButton({
    super.key,
    this.radius,
    this.onTap,
    this.showBorder = true,
    this.borderColor,
    this.borderWidth,
    this.showShadow = true,
    this.imagePath,
    this.showRipple = true,
  });

  @override
  Widget build(BuildContext context) {
    final Widget iconWidget = CircularAppIcon(
      radius: radius,
      showBorder: showBorder,
      borderColor: borderColor,
      borderWidth: borderWidth,
      showShadow: showShadow,
      imagePath: imagePath,
    );

    if (onTap == null) return iconWidget;

    return showRipple
        ? Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(
              (radius ?? ResponsiveHelper.getValue(context, 30.0, 35.0, 40.0)) + 
              (showBorder ? (borderWidth ?? 2.0) : 0),
            ),
            child: InkWell(
              borderRadius: BorderRadius.circular(
                (radius ?? ResponsiveHelper.getValue(context, 30.0, 35.0, 40.0)) + 
                (showBorder ? (borderWidth ?? 2.0) : 0),
              ),
              onTap: onTap,
              child: iconWidget,
            ),
          )
        : GestureDetector(
            onTap: onTap,
            child: iconWidget,
          );
  }
}