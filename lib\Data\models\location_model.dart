import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationModel {
  final int id;
  final String name;
  final LatLng coordinates;

  LocationModel({
    required this.id,
    required this.name,
    required this.coordinates,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      id: json['id'],
      name: json['name'] ?? '',
      coordinates: LatLng(
        (json['latitude'] as num).toDouble(),
        (json['longitude'] as num).toDouble(),
      ),
    );
  }
}
